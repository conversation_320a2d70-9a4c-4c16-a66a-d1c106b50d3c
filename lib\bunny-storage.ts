import { v4 as uuidv4 } from 'uuid'

interface BunnyStorageConfig {
  storageZoneName: string
  accessKey: string
  pullZoneUrl: string
  region?: string
}

interface UploadOptions {
  folder?: string
  filename?: string
  contentType?: string
  optimize?: boolean
  maxSize?: number
}

interface UploadResult {
  success: boolean
  url?: string
  filename?: string
  size?: number
  error?: string
}

class BunnyStorage {
  private config: BunnyStorageConfig
  private baseUrl: string

  constructor(config: BunnyStorageConfig) {
    this.config = config
    // Construct the correct Bunny CDN Storage API URL based on region
    // According to Bunny CDN docs: https://docs.bunny.net/reference/put_-storagezonename-path-filename
    let hostname: string

    if (!config.region || config.region === 'storage' || config.region === 'de') {
      // Frankfurt, DE (default region)
      hostname = 'storage.bunnycdn.com'
    } else {
      // Other regions: {region}.storage.bunnycdn.com
      hostname = `${config.region}.storage.bunnycdn.com`
    }

    this.baseUrl = `https://${hostname}/${config.storageZoneName}`
  }

  /**
   * Test connection to Bunny Storage
   */
  async testConnection(): Promise<boolean> {
    try {
      // Test by trying to list files in the root directory
      // This is a safer way to test connectivity without creating files
      const testUrl = `${this.baseUrl}/`
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'AccessKey': this.config.accessKey
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      // 200 (success), 404 (not found), or 403 (forbidden) all indicate we can connect
      // 401 would indicate authentication issues
      return response.status === 200 || response.status === 404 || response.status === 403
    } catch (error) {
      console.error('Bunny Storage connection test failed:', error)
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('Connection test timed out')
      }
      return false
    }
  }

  /**
   * Upload a file to Bunny Storage
   */
  async uploadFile(
    file: File | Buffer,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      const {
        folder = 'uploads',
        filename,
        contentType,
        optimize = true,
        maxSize = 10 * 1024 * 1024 // 10MB default
      } = options

      // Validate file size
      const fileSize = file instanceof File ? file.size : file.length
      if (fileSize > maxSize) {
        return {
          success: false,
          error: `File size exceeds maximum allowed size of ${maxSize / (1024 * 1024)}MB`
        }
      }

      // Generate filename if not provided
      const finalFilename = filename || this.generateFilename(
        file instanceof File ? file.name : 'file',
        file instanceof File ? file.type : contentType || 'application/octet-stream'
      )

      // Construct the upload path
      const uploadPath = `${folder}/${finalFilename}`
      const uploadUrl = `${this.baseUrl}/${uploadPath}`

      console.log(`Bunny CDN Upload URL: ${uploadUrl}`)
      console.log(`File size: ${fileSize} bytes`)

      // Handle different file types for upload
      let bodyData: BodyInit
      if (file instanceof File) {
        bodyData = file
      } else if (Buffer.isBuffer(file)) {
        bodyData = new Blob([new Uint8Array(file)])
      } else {
        bodyData = new Blob([file])
      }

      // Upload to Bunny Storage with timeout and better error handling
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

      try {
        const response = await fetch(uploadUrl, {
          method: 'PUT',
          headers: {
            'AccessKey': this.config.accessKey,
            'Content-Type': file instanceof File ? file.type : contentType || 'application/octet-stream',
            'Content-Length': fileSize.toString()
          },
          body: bodyData,
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Unknown error')
          throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`)
        }
      } catch (error) {
        clearTimeout(timeoutId)
        if (error instanceof Error && error.name === 'AbortError') {
          throw new Error('Upload timeout - please check your internet connection and try again')
        }
        throw error
      }

      // Return success result
      const publicUrl = `${this.config.pullZoneUrl}/${uploadPath}`
      
      return {
        success: true,
        url: publicUrl,
        filename: finalFilename,
        size: fileSize
      }

    } catch (error) {
      console.error('Bunny Storage upload error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Upload multiple files
   */
  async uploadMultipleFiles(
    files: (File | Buffer)[],
    options: UploadOptions = {}
  ): Promise<UploadResult[]> {
    const uploadPromises = files.map(file => this.uploadFile(file, options))
    return Promise.all(uploadPromises)
  }

  /**
   * Delete a file from Bunny Storage
   */
  async deleteFile(filepath: string): Promise<boolean> {
    try {
      const deleteUrl = `${this.baseUrl}/${filepath}`
      
      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'AccessKey': this.config.accessKey
        }
      })

      return response.ok
    } catch (error) {
      console.error('Bunny Storage delete error:', error)
      return false
    }
  }

  /**
   * Get file info
   */
  async getFileInfo(filepath: string): Promise<any> {
    try {
      const infoUrl = `${this.baseUrl}/${filepath}`
      
      const response = await fetch(infoUrl, {
        method: 'HEAD',
        headers: {
          'AccessKey': this.config.accessKey
        }
      })

      if (!response.ok) {
        return null
      }

      return {
        size: response.headers.get('content-length'),
        type: response.headers.get('content-type'),
        lastModified: response.headers.get('last-modified')
      }
    } catch (error) {
      console.error('Bunny Storage file info error:', error)
      return null
    }
  }

  /**
   * Generate optimized filename
   */
  private generateFilename(originalName: string, mimeType: string): string {
    const timestamp = Date.now()
    const uuid = uuidv4().split('-')[0]
    const extension = this.getFileExtension(originalName, mimeType)
    
    // Clean the original name
    const cleanName = originalName
      .replace(/\.[^/.]+$/, '') // Remove extension
      .replace(/[^a-zA-Z0-9]/g, '-') // Replace special chars with dash
      .toLowerCase()
      .substring(0, 50) // Limit length

    return `${cleanName}-${timestamp}-${uuid}${extension}`
  }

  /**
   * Get file extension from name or mime type
   */
  private getFileExtension(filename: string, mimeType: string): string {
    // Try to get extension from filename first
    const match = filename.match(/\.[^/.]+$/)
    if (match) {
      return match[0]
    }

    // Fallback to mime type mapping
    const mimeToExt: Record<string, string> = {
      'image/jpeg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/webp': '.webp',
      'image/svg+xml': '.svg',
      'application/pdf': '.pdf',
      'text/plain': '.txt',
      'application/json': '.json',
      'text/csv': '.csv',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx'
    }

    return mimeToExt[mimeType] || ''
  }

  /**
   * Validate file type
   */
  isValidFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.includes(file.type) || 
           allowedTypes.some(type => file.name.toLowerCase().endsWith(type.replace('*', '')))
  }

  /**
   * Get optimized image URL with transformations
   */
  getOptimizedImageUrl(
    originalUrl: string,
    options: {
      width?: number
      height?: number
      quality?: number
      format?: 'webp' | 'jpg' | 'png'
    } = {}
  ): string {
    const { width, height, quality = 85, format } = options

    // Bunny CDN image optimization parameters
    const params = new URLSearchParams()

    if (width) params.append('width', width.toString())
    if (height) params.append('height', height.toString())
    if (quality) params.append('quality', quality.toString())
    if (format) params.append('format', format)

    const queryString = params.toString()
    return queryString ? `${originalUrl}?${queryString}` : originalUrl
  }
}

/**
 * Local Storage Fallback for Development
 * This class provides a fallback when Bunny CDN is not configured
 */
class LocalStorageFallback {
  async uploadFile(
    file: File | Buffer,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      console.log('Using local storage fallback for file upload')

      const {
        folder = 'uploads',
        filename,
        maxSize = 10 * 1024 * 1024 // 10MB default
      } = options

      // Validate file size
      const fileSize = file instanceof File ? file.size : file.length
      if (fileSize > maxSize) {
        return {
          success: false,
          error: `File size exceeds maximum allowed size of ${maxSize / (1024 * 1024)}MB`
        }
      }

      // Generate filename if not provided
      const finalFilename = filename || this.generateFilename(
        file instanceof File ? file.name : 'file',
        file instanceof File ? file.type : 'application/octet-stream'
      )

      // For development, return a mock URL
      const mockUrl = `/uploads/${folder}/${finalFilename}`

      console.log(`Mock upload successful: ${mockUrl}`)

      return {
        success: true,
        url: mockUrl,
        filename: finalFilename,
        size: fileSize
      }

    } catch (error) {
      console.error('Local storage fallback error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  async uploadMultipleFiles(
    files: (File | Buffer)[],
    options: UploadOptions = {}
  ): Promise<UploadResult[]> {
    const uploadPromises = files.map(file => this.uploadFile(file, options))
    return Promise.all(uploadPromises)
  }

  async deleteFile(filepath: string): Promise<boolean> {
    console.log(`Mock delete: ${filepath}`)
    return true
  }

  async getFileInfo(filepath: string): Promise<any> {
    console.log(`Mock file info: ${filepath}`)
    return {
      size: '1024',
      type: 'application/octet-stream',
      lastModified: new Date().toISOString()
    }
  }

  private generateFilename(originalName: string, mimeType: string): string {
    const timestamp = Date.now()
    const uuid = uuidv4().split('-')[0]
    const extension = this.getFileExtension(originalName, mimeType)

    // Clean the original name
    const cleanName = originalName
      .replace(/\.[^/.]+$/, '') // Remove extension
      .replace(/[^a-zA-Z0-9]/g, '-') // Replace special chars with dash
      .toLowerCase()
      .substring(0, 50) // Limit length

    return `${cleanName}-${timestamp}-${uuid}${extension}`
  }

  private getFileExtension(filename: string, mimeType: string): string {
    // Try to get extension from filename first
    const match = filename.match(/\.[^/.]+$/)
    if (match) {
      return match[0]
    }

    // Fallback to mime type mapping
    const mimeToExt: Record<string, string> = {
      'image/jpeg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/webp': '.webp',
      'image/svg+xml': '.svg',
      'application/pdf': '.pdf',
      'text/plain': '.txt',
      'application/json': '.json',
      'text/csv': '.csv',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx'
    }

    return mimeToExt[mimeType] || ''
  }

  isValidFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.includes(file.type) ||
           allowedTypes.some(type => file.name.toLowerCase().endsWith(type.replace('*', '')))
  }

  getOptimizedImageUrl(
    originalUrl: string,
    options: {
      width?: number
      height?: number
      quality?: number
      format?: 'webp' | 'jpg' | 'png'
    } = {}
  ): string {
    // For local development, just return the original URL
    return originalUrl
  }
}

// Create singleton instance
let bunnyStorage: BunnyStorage | null = null

export function getBunnyStorage(): BunnyStorage {
  if (!bunnyStorage) {
    const config: BunnyStorageConfig = {
      storageZoneName: process.env.BUNNY_STORAGE_ZONE_NAME || '',
      accessKey: process.env.BUNNY_STORAGE_ACCESS_KEY || '',
      pullZoneUrl: process.env.BUNNY_PULL_ZONE_URL || '',
      region: process.env.BUNNY_STORAGE_REGION || 'storage'
    }

    // Check if Bunny CDN is properly configured
    if (!config.storageZoneName || !config.accessKey || !config.pullZoneUrl) {
      console.warn('Bunny Storage configuration is incomplete. Using local storage fallback for development.')
      // Return a mock storage for development
      return new LocalStorageFallback() as any
    }

    bunnyStorage = new BunnyStorage(config)
  }

  return bunnyStorage
}

/**
 * Get Bunny Storage with connection testing
 */
export async function getBunnyStorageWithTest(): Promise<BunnyStorage> {
  const storage = getBunnyStorage()

  // If it's the fallback, return immediately
  if (storage instanceof LocalStorageFallback) {
    return storage as any
  }

  // Test connection for real Bunny Storage
  const canConnect = await (storage as BunnyStorage).testConnection()
  if (!canConnect) {
    console.warn('Cannot connect to Bunny Storage. Using local storage fallback.')
    return new LocalStorageFallback() as any
  }

  return storage
}

// File type constants
export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml'
]

export const ALLOWED_DOCUMENT_TYPES = [
  'application/pdf',
  'text/plain',
  'application/json',
  'text/csv',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation'
]

export const MAX_FILE_SIZES = {
  image: 5 * 1024 * 1024, // 5MB
  document: 10 * 1024 * 1024, // 10MB
  video: 50 * 1024 * 1024 // 50MB
}

export { BunnyStorage }
export type { UploadOptions, UploadResult }
