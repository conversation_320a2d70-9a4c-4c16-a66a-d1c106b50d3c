import { v4 as uuidv4 } from 'uuid'

interface BunnyStorageConfig {
  storageZoneName: string
  accessKey: string
  pullZoneUrl: string
  region?: string
}

interface UploadOptions {
  folder?: string
  filename?: string
  contentType?: string
  optimize?: boolean
  maxSize?: number
}

interface UploadResult {
  success: boolean
  url?: string
  filename?: string
  size?: number
  error?: string
}

class BunnyStorage {
  private config: BunnyStorageConfig
  private baseUrl: string

  constructor(config: BunnyStorageConfig) {
    this.config = config
    this.baseUrl = `https://${config.region || 'storage'}.bunnycdn.com/${config.storageZoneName}`
  }

  /**
   * Upload a file to Bunny Storage
   */
  async uploadFile(
    file: File | Buffer,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      const {
        folder = 'uploads',
        filename,
        contentType,
        optimize = true,
        maxSize = 10 * 1024 * 1024 // 10MB default
      } = options

      // Validate file size
      const fileSize = file instanceof File ? file.size : file.length
      if (fileSize > maxSize) {
        return {
          success: false,
          error: `File size exceeds maximum allowed size of ${maxSize / (1024 * 1024)}MB`
        }
      }

      // Generate filename if not provided
      const finalFilename = filename || this.generateFilename(
        file instanceof File ? file.name : 'file',
        file instanceof File ? file.type : contentType || 'application/octet-stream'
      )

      // Construct the upload path
      const uploadPath = `${folder}/${finalFilename}`
      const uploadUrl = `${this.baseUrl}/${uploadPath}`

      // Handle different file types for upload
      let bodyData: BodyInit
      if (file instanceof File) {
        bodyData = file
      } else if (Buffer.isBuffer(file)) {
        bodyData = new Blob([new Uint8Array(file)])
      } else {
        bodyData = new Blob([file])
      }

      // Upload to Bunny Storage
      const response = await fetch(uploadUrl, {
        method: 'PUT',
        headers: {
          'AccessKey': this.config.accessKey,
          'Content-Type': file instanceof File ? file.type : contentType || 'application/octet-stream',
          'Content-Length': fileSize.toString()
        },
        body: bodyData
      })

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status} ${response.statusText}`)
      }

      // Return success result
      const publicUrl = `${this.config.pullZoneUrl}/${uploadPath}`
      
      return {
        success: true,
        url: publicUrl,
        filename: finalFilename,
        size: fileSize
      }

    } catch (error) {
      console.error('Bunny Storage upload error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Upload multiple files
   */
  async uploadMultipleFiles(
    files: (File | Buffer)[],
    options: UploadOptions = {}
  ): Promise<UploadResult[]> {
    const uploadPromises = files.map(file => this.uploadFile(file, options))
    return Promise.all(uploadPromises)
  }

  /**
   * Delete a file from Bunny Storage
   */
  async deleteFile(filepath: string): Promise<boolean> {
    try {
      const deleteUrl = `${this.baseUrl}/${filepath}`
      
      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'AccessKey': this.config.accessKey
        }
      })

      return response.ok
    } catch (error) {
      console.error('Bunny Storage delete error:', error)
      return false
    }
  }

  /**
   * Get file info
   */
  async getFileInfo(filepath: string): Promise<any> {
    try {
      const infoUrl = `${this.baseUrl}/${filepath}`
      
      const response = await fetch(infoUrl, {
        method: 'HEAD',
        headers: {
          'AccessKey': this.config.accessKey
        }
      })

      if (!response.ok) {
        return null
      }

      return {
        size: response.headers.get('content-length'),
        type: response.headers.get('content-type'),
        lastModified: response.headers.get('last-modified')
      }
    } catch (error) {
      console.error('Bunny Storage file info error:', error)
      return null
    }
  }

  /**
   * Generate optimized filename
   */
  private generateFilename(originalName: string, mimeType: string): string {
    const timestamp = Date.now()
    const uuid = uuidv4().split('-')[0]
    const extension = this.getFileExtension(originalName, mimeType)
    
    // Clean the original name
    const cleanName = originalName
      .replace(/\.[^/.]+$/, '') // Remove extension
      .replace(/[^a-zA-Z0-9]/g, '-') // Replace special chars with dash
      .toLowerCase()
      .substring(0, 50) // Limit length

    return `${cleanName}-${timestamp}-${uuid}${extension}`
  }

  /**
   * Get file extension from name or mime type
   */
  private getFileExtension(filename: string, mimeType: string): string {
    // Try to get extension from filename first
    const match = filename.match(/\.[^/.]+$/)
    if (match) {
      return match[0]
    }

    // Fallback to mime type mapping
    const mimeToExt: Record<string, string> = {
      'image/jpeg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/webp': '.webp',
      'image/svg+xml': '.svg',
      'application/pdf': '.pdf',
      'text/plain': '.txt',
      'application/json': '.json',
      'text/csv': '.csv',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx'
    }

    return mimeToExt[mimeType] || ''
  }

  /**
   * Validate file type
   */
  isValidFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.includes(file.type) || 
           allowedTypes.some(type => file.name.toLowerCase().endsWith(type.replace('*', '')))
  }

  /**
   * Get optimized image URL with transformations
   */
  getOptimizedImageUrl(
    originalUrl: string, 
    options: {
      width?: number
      height?: number
      quality?: number
      format?: 'webp' | 'jpg' | 'png'
    } = {}
  ): string {
    const { width, height, quality = 85, format } = options
    
    // Bunny CDN image optimization parameters
    const params = new URLSearchParams()
    
    if (width) params.append('width', width.toString())
    if (height) params.append('height', height.toString())
    if (quality) params.append('quality', quality.toString())
    if (format) params.append('format', format)
    
    const queryString = params.toString()
    return queryString ? `${originalUrl}?${queryString}` : originalUrl
  }
}

// Create singleton instance
let bunnyStorage: BunnyStorage | null = null

export function getBunnyStorage(): BunnyStorage {
  if (!bunnyStorage) {
    const config: BunnyStorageConfig = {
      storageZoneName: process.env.BUNNY_STORAGE_ZONE_NAME || '',
      accessKey: process.env.BUNNY_STORAGE_ACCESS_KEY || '',
      pullZoneUrl: process.env.BUNNY_PULL_ZONE_URL || '',
      region: process.env.BUNNY_STORAGE_REGION || 'storage'
    }

    if (!config.storageZoneName || !config.accessKey || !config.pullZoneUrl) {
      throw new Error('Bunny Storage configuration is incomplete. Please check your environment variables.')
    }

    bunnyStorage = new BunnyStorage(config)
  }

  return bunnyStorage
}

// File type constants
export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml'
]

export const ALLOWED_DOCUMENT_TYPES = [
  'application/pdf',
  'text/plain',
  'application/json',
  'text/csv',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation'
]

export const MAX_FILE_SIZES = {
  image: 5 * 1024 * 1024, // 5MB
  document: 10 * 1024 * 1024, // 10MB
  video: 50 * 1024 * 1024 // 50MB
}

export { BunnyStorage }
export type { UploadOptions, UploadResult }
