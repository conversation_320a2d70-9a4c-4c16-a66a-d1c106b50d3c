'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { 
  ArrowLeftIcon,
  CloudArrowUpIcon,
  PlayIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface LessonFormData {
  title: string
  description: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  order: number
  duration: number
  isPublished: boolean
  isFree: boolean
  content: string
  videoUrl: string
}

export default function EditLessonPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params?.id as string
  const sectionId = params?.sectionId as string
  const chapterId = params?.chapterId as string
  const lessonId = params?.lessonId as string

  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [uploadingVideo, setUploadingVideo] = useState(false)
  const [formData, setFormData] = useState<LessonFormData>({
    title: '',
    description: '',
    type: 'VIDEO',
    order: 1,
    duration: 0,
    isPublished: false,
    isFree: false,
    content: '',
    videoUrl: ''
  })

  const lessonTypes = [
    { value: 'VIDEO', label: 'Video Lesson', icon: PlayIcon },
    { value: 'TEXT', label: 'Text Content', icon: DocumentTextIcon },
    { value: 'QUIZ', label: 'Quiz', icon: AcademicCapIcon },
    { value: 'ASSIGNMENT', label: 'Assignment', icon: ClipboardDocumentListIcon },
    { value: 'DOCUMENT', label: 'Document', icon: DocumentTextIcon }
  ]

  useEffect(() => {
    if (courseId && sectionId && chapterId && lessonId) {
      fetchLesson()
    }
  }, [courseId, sectionId, chapterId, lessonId])

  const fetchLesson = async () => {
    try {
      setInitialLoading(true)
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons/${lessonId}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch lesson')
      }

      const result = await response.json()
      const data = result.data || result
      const lesson = data.lesson || data

      setFormData({
        title: lesson.title || '',
        description: lesson.description || '',
        type: lesson.type || 'VIDEO',
        order: lesson.order || 1,
        duration: lesson.duration || 0,
        isPublished: lesson.isPublished || false,
        isFree: lesson.isFree || false,
        content: lesson.content || '',
        videoUrl: lesson.video?.url || ''
      })
    } catch (error) {
      console.error('Error fetching lesson:', error)
      toast.error('Failed to load lesson data')
      router.push(`/admin/courses/${courseId}`)
    } finally {
      setInitialLoading(false)
    }
  }

  const handleInputChange = (field: keyof LessonFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleVideoUpload = async (file: File) => {
    if (!file) return

    try {
      setUploadingVideo(true)
      
      const uploadFormData = new FormData()
      uploadFormData.append('video', file)
      uploadFormData.append('courseId', courseId)
      uploadFormData.append('lessonTitle', formData.title || 'Untitled Lesson')

      const response = await fetch('/api/admin/upload/video', {
        method: 'POST',
        body: uploadFormData
      })

      if (!response.ok) {
        throw new Error('Failed to upload video')
      }

      const result = await response.json()
      const data = result.data || result

      setFormData(prev => ({
        ...prev,
        videoUrl: data.videoUrl || data.url,
        duration: data.duration || 0
      }))

      toast.success('Video uploaded successfully!')
    } catch (error: any) {
      console.error('Error uploading video:', error)
      toast.error(error.message || 'Failed to upload video')
    } finally {
      setUploadingVideo(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast.error('Lesson title is required')
      return
    }

    try {
      setLoading(true)
      console.log('Updating lesson:', formData)
      
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons/${lessonId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        const error = await response.json()
        console.error('API Error:', error)
        throw new Error(error.message || 'Failed to update lesson')
      }

      const result = await response.json()
      console.log('API Response:', result)
      
      const data = result.data || result
      toast.success(data.message || 'Lesson updated successfully!')
      
      router.push(`/admin/courses/${courseId}`)

    } catch (error: any) {
      console.error('Error updating lesson:', error)
      toast.error(error.message || 'Failed to update lesson')
    } finally {
      setLoading(false)
    }
  }

  if (initialLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading lesson data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href={`/admin/courses/${courseId}`}>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </motion.button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Edit Lesson
            </h1>
            <p className="text-gray-600 mt-2">Update lesson information and content</p>
          </div>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Lesson Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lesson Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder="Enter lesson title"
                required
              />
            </div>

            {/* Lesson Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Lesson Type *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {lessonTypes.map((type) => {
                  const Icon = type.icon
                  return (
                    <motion.button
                      key={type.value}
                      type="button"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleInputChange('type', type.value)}
                      className={`p-4 border-2 rounded-xl transition-all duration-200 ${
                        formData.type === type.value
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 bg-white/50 text-gray-600 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="w-6 h-6 mx-auto mb-2" />
                      <div className="text-sm font-medium">{type.label}</div>
                    </motion.button>
                  )
                })}
              </div>
            </div>

            {/* Video Upload/Display (only for VIDEO type) */}
            {formData.type === 'VIDEO' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Video {formData.videoUrl ? 'Update' : 'Upload'} *
                </label>
                {!formData.videoUrl ? (
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center">
                    <CloudArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <div className="text-gray-600 mb-4">
                      <p className="text-lg font-medium">Upload your video</p>
                      <p className="text-sm">Drag and drop or click to browse</p>
                    </div>
                    <input
                      type="file"
                      accept="video/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleVideoUpload(file)
                      }}
                      className="hidden"
                      id="video-upload"
                      disabled={uploadingVideo}
                    />
                    <label
                      htmlFor="video-upload"
                      className={`inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-medium cursor-pointer hover:bg-blue-700 transition-colors duration-200 ${
                        uploadingVideo ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                    >
                      {uploadingVideo ? 'Uploading...' : 'Choose Video File'}
                    </label>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <PlayIcon className="w-6 h-6 text-green-600" />
                          <div>
                            <p className="text-green-800 font-medium">Video uploaded</p>
                            <p className="text-green-600 text-sm">Duration: {Math.round(formData.duration / 60)} minutes</p>
                          </div>
                        </div>
                        <input
                          type="file"
                          accept="video/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0]
                            if (file) handleVideoUpload(file)
                          }}
                          className="hidden"
                          id="video-replace"
                          disabled={uploadingVideo}
                        />
                        <label
                          htmlFor="video-replace"
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium cursor-pointer hover:bg-blue-700 transition-colors duration-200"
                        >
                          {uploadingVideo ? 'Uploading...' : 'Replace Video'}
                        </label>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Rest of the form continues... */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link href={`/admin/courses/${courseId}`}>
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200"
                >
                  Cancel
                </motion.button>
              </Link>
              <motion.button
                type="submit"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                disabled={loading || uploadingVideo}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
              >
                {loading ? 'Updating...' : 'Update Lesson'}
              </motion.button>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  )
}
