'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useParams, useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import {
  ArrowLeftIcon,
  PlayIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ClipboardDocumentListIcon,
  DocumentIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import VideoUpload from '@/components/admin/video-upload'

interface LessonFormData {
  title: string
  description: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  order: number
  duration: number
  isPublished: boolean
  isFree: boolean
  content: string
  videoUrl: string
}

export default function EditLessonPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params?.id as string
  const sectionId = params?.sectionId as string
  const chapterId = params?.chapterId as string
  const lessonId = params?.lessonId as string

  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [formData, setFormData] = useState<LessonFormData>({
    title: '',
    description: '',
    type: 'VIDEO',
    order: 1,
    duration: 0,
    isPublished: false,
    isFree: false,
    content: '',
    videoUrl: ''
  })

  const lessonTypes = [
    {
      value: 'VIDEO',
      label: 'Video Lesson',
      icon: PlayIcon,
      description: 'Upload and stream video content'
    },
    {
      value: 'TEXT',
      label: 'Text Content',
      icon: DocumentTextIcon,
      description: 'Rich text content and articles'
    },
    {
      value: 'QUIZ',
      label: 'Quiz',
      icon: AcademicCapIcon,
      description: 'Interactive quizzes and assessments'
    },
    {
      value: 'ASSIGNMENT',
      label: 'Assignment',
      icon: ClipboardDocumentListIcon,
      description: 'Homework and practical exercises'
    },
    {
      value: 'DOCUMENT',
      label: 'Document',
      icon: DocumentIcon,
      description: 'PDFs, slides, and downloadable files'
    }
  ]

  useEffect(() => {
    if (courseId && sectionId && chapterId && lessonId) {
      fetchLesson()
    }
  }, [courseId, sectionId, chapterId, lessonId])

  const fetchLesson = async () => {
    try {
      setInitialLoading(true)
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons/${lessonId}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch lesson')
      }

      const result = await response.json()
      const data = result.data || result
      const lesson = data.lesson || data

      setFormData({
        title: lesson.title || '',
        description: lesson.description || '',
        type: lesson.type || 'VIDEO',
        order: lesson.order || 1,
        duration: lesson.duration || 0,
        isPublished: lesson.isPublished || false,
        isFree: lesson.isFree || false,
        content: lesson.content || '',
        videoUrl: lesson.video?.url || ''
      })
    } catch (error) {
      console.error('Error fetching lesson:', error)
      toast.error('Failed to load lesson data')
      router.push(`/admin/courses/${courseId}`)
    } finally {
      setInitialLoading(false)
    }
  }

  const handleInputChange = (field: keyof LessonFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleVideoUploadComplete = (video: any) => {
    console.log('Video upload completed:', video)
    setFormData(prev => ({
      ...prev,
      videoUrl: video.url,
      duration: video.duration || 0
    }))
    toast.success('Video updated successfully!')
  }

  const handleVideoUploadError = (error: string) => {
    console.error('Video upload error:', error)
    toast.error(error)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast.error('Lesson title is required')
      return
    }

    try {
      setLoading(true)
      console.log('Updating lesson:', formData)
      
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons/${lessonId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        const error = await response.json()
        console.error('API Error:', error)
        throw new Error(error.message || 'Failed to update lesson')
      }

      const result = await response.json()
      console.log('API Response:', result)
      
      const data = result.data || result
      toast.success(data.message || 'Lesson updated successfully!')
      
      router.push(`/admin/courses/${courseId}`)

    } catch (error: any) {
      console.error('Error updating lesson:', error)
      toast.error(error.message || 'Failed to update lesson')
    } finally {
      setLoading(false)
    }
  }

  if (initialLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading lesson data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href={`/admin/courses/${courseId}`}>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </motion.button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Edit Lesson
            </h1>
            <p className="text-gray-600 mt-2">Update lesson information and content</p>
          </div>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Lesson Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lesson Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder="Enter lesson title"
                required
              />
            </div>

            {/* Lesson Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Lesson Type *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {lessonTypes.map((type) => {
                  const Icon = type.icon
                  return (
                    <motion.button
                      key={type.value}
                      type="button"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleInputChange('type', type.value)}
                      className={`p-4 border-2 rounded-xl transition-all duration-200 ${
                        formData.type === type.value
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 bg-white/50 text-gray-600 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="w-6 h-6 mx-auto mb-2" />
                      <div className="text-sm font-medium">{type.label}</div>
                    </motion.button>
                  )
                })}
              </div>
            </div>

            {/* Video Upload/Display (only for VIDEO type) */}
            {formData.type === 'VIDEO' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Video Management
                </label>
                <VideoUpload
                  lessonId={lessonId}
                  existingVideo={formData.videoUrl ? {
                    id: `video-${lessonId}`,
                    url: formData.videoUrl,
                    filename: `lesson-${lessonId}-video`,
                    originalName: 'Lesson Video',
                    duration: formData.duration,
                    size: 0 // Size not available in form data
                  } : undefined}
                  onUploadComplete={handleVideoUploadComplete}
                  onUploadError={handleVideoUploadError}
                />
              </div>
            )}

            {/* Rest of the form continues... */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link href={`/admin/courses/${courseId}`}>
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200"
                >
                  Cancel
                </motion.button>
              </Link>
              <motion.button
                type="submit"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                disabled={loading || uploadingVideo}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
              >
                {loading ? 'Updating...' : 'Update Lesson'}
              </motion.button>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  )
}
