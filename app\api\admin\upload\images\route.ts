import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { getBunnyStorage } from '@/lib/bunny-storage'

// POST /api/admin/upload/images - Upload image files
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const formData = await request.formData()
      const file = formData.get('image') as File
      const type = formData.get('type') as string || 'general'
      const aspectRatio = formData.get('aspectRatio') as string || 'video'

      if (!file) {
        return APIResponse.error('No image file provided', 400)
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        return APIResponse.error(`File type ${file.type} is not allowed. Please use JPG, PNG, WebP, or GIF.`, 400)
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        return APIResponse.error('Image file too large. Maximum size is 5MB', 400)
      }

      // Get Bunny storage instance
      const bunnyStorage = getBunnyStorage()

      // Generate unique filename
      const timestamp = Date.now()
      const extension = file.name.split('.').pop() || 'jpg'
      const uniqueFilename = `${type}_${timestamp}.${extension}`

      // Upload image to Bunny CDN
      const uploadResult = await bunnyStorage.uploadFile(file, {
        folder: `courses/images`,
        filename: uniqueFilename,
        maxSize: maxSize
      })

      if (!uploadResult.success) {
        return APIResponse.error(
          `Failed to upload image: ${uploadResult.error}`,
          500
        )
      }

      // Create image object
      const imageData = {
        id: `image-${timestamp}`,
        url: uploadResult.url!,
        filename: uniqueFilename,
        originalName: file.name,
        type: file.type,
        size: uploadResult.size!,
        aspectRatio: aspectRatio,
        uploadedAt: new Date().toISOString(),
        imageType: type
      }

      return APIResponse.success({
        message: 'Image uploaded successfully',
        ...imageData
      })

    } catch (error) {
      console.error('Error uploading image:', error)
      return APIResponse.error('Failed to upload image', 500)
    }
  }
)
