import { NextRequest } from 'next/server'
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

// POST /api/admin/upload/images - Upload image files
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const formData = await request.formData()
      const file = formData.get('image') as File
      const type = formData.get('type') as string || 'general'
      const aspectRatio = formData.get('aspectRatio') as string || 'video'

      if (!file) {
        return APIResponse.error('No image file provided', 400)
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        return APIResponse.error(`File type ${file.type} is not allowed. Please use JPG, PNG, WebP, or GIF.`, 400)
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        return APIResponse.error('Image file too large. Maximum size is 5MB', 400)
      }

      // Create upload directory
      const uploadDir = join(process.cwd(), 'public', 'uploads', 'images')
      if (!existsSync(uploadDir)) {
        await mkdir(uploadDir, { recursive: true })
      }

      // Generate unique filename
      const timestamp = Date.now()
      const extension = file.name.split('.').pop()
      const filename = `${type}_${timestamp}.${extension}`
      const filepath = join(uploadDir, filename)

      // Save file
      const bytes = await file.arrayBuffer()
      const buffer = Buffer.from(bytes)
      await writeFile(filepath, buffer)

      // Generate public URL
      const imageUrl = `/uploads/images/${filename}`

      // Create image object
      const imageData = {
        id: `image-${timestamp}`,
        url: imageUrl,
        filename: filename,
        originalName: file.name,
        type: file.type,
        size: file.size,
        aspectRatio: aspectRatio,
        uploadedAt: new Date().toISOString(),
        imageType: type
      }

      // TODO: In production, you would:
      // 1. Upload to Bunny CDN or your preferred image hosting service
      // 2. Generate multiple sizes/thumbnails
      // 3. Optimize images (compress, convert to WebP)
      // 4. Extract image metadata (dimensions, EXIF data)
      // 5. Store image record in database

      return APIResponse.success({
        message: 'Image uploaded successfully',
        ...imageData
      })

    } catch (error) {
      console.error('Error uploading image:', error)
      return APIResponse.error('Failed to upload image', 500)
    }
  }
)

// Helper functions for production use
async function uploadToBunnyCDN(filepath: string, filename: string) {
  // Upload to Bunny CDN
  // const bunnyApiKey = process.env.BUNNY_API_KEY
  // const bunnyStorageZone = process.env.BUNNY_STORAGE_ZONE
  
  return {
    url: `https://your-bunny-cdn.com/images/${filename}`,
    thumbnailUrl: `https://your-bunny-cdn.com/thumbnails/${filename}`
  }
}

async function generateImageThumbnails(imagePath: string, filename: string) {
  // Generate multiple sizes using sharp or similar
  // const sharp = require('sharp')
  
  const sizes = [
    { name: 'thumbnail', width: 150, height: 150 },
    { name: 'small', width: 300, height: 200 },
    { name: 'medium', width: 600, height: 400 },
    { name: 'large', width: 1200, height: 800 }
  ]

  const thumbnails = []
  
  for (const size of sizes) {
    const outputPath = `thumbnails/${size.name}_${filename}`
    // await sharp(imagePath)
    //   .resize(size.width, size.height, { fit: 'cover' })
    //   .jpeg({ quality: 80 })
    //   .toFile(outputPath)
    
    thumbnails.push({
      size: size.name,
      url: `/uploads/images/${outputPath}`,
      width: size.width,
      height: size.height
    })
  }

  return thumbnails
}

async function optimizeImage(imagePath: string, outputPath: string) {
  // Optimize image using sharp
  // const sharp = require('sharp')
  
  // await sharp(imagePath)
  //   .jpeg({ quality: 85, progressive: true })
  //   .png({ compressionLevel: 8 })
  //   .webp({ quality: 85 })
  //   .toFile(outputPath)
}

async function extractImageMetadata(imagePath: string) {
  // Extract image metadata
  // const sharp = require('sharp')
  
  // const metadata = await sharp(imagePath).metadata()
  
  return {
    width: 1920,
    height: 1080,
    format: 'jpeg',
    hasAlpha: false,
    colorSpace: 'srgb'
  }
}
