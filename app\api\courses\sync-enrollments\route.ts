import { NextRequest } from 'next/server'
import { APIResponse, createAPIHandler } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// POST /api/courses/sync-enrollments - Get user enrollments (no external sync needed)
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (_request: NextRequest, { user }: { user: any }) => {
    try {
      // Get user's current enrollments from database
      const enrollments = await prisma.courseEnrollment.findMany({
        where: {
          userId: user.id,
          status: 'active'
        },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              slug: true,
              thumbnailImage: true,
              price: true,
              category: true,
              level: true,
              instructor: {
                select: {
                  id: true,
                  name: true,
                  image: true
                }
              }
            }
          }
        },
        orderBy: { enrolledAt: 'desc' }
      })

      return APIResponse.success({
        message: 'Enrollments retrieved successfully',
        data: {
          enrollments,
          summary: {
            totalEnrollments: enrollments.length,
            syncedEnrollments: enrollments.length,
            lastSyncAt: new Date().toISOString()
          }
        }
      })

    } catch (error: any) {
      console.error('❌ Error retrieving enrollments:', error)
      return APIResponse.error(
        'Failed to retrieve enrollments: ' + error.message,
        500
      )
    }
  }
)

// GET /api/courses/sync-enrollments - Check enrollment status
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (_request: NextRequest, { user }: { user: any }) => {
    try {
      // Get enrollment count and last access info
      const enrollmentCount = await prisma.courseEnrollment.count({
        where: {
          userId: user.id,
          status: 'active'
        }
      })

      const lastEnrollment = await prisma.courseEnrollment.findFirst({
        where: { userId: user.id },
        orderBy: { updatedAt: 'desc' },
        select: { updatedAt: true }
      })

      return APIResponse.success({
        data: {
          totalEnrollments: enrollmentCount,
          lastAccessAt: lastEnrollment?.updatedAt?.toISOString() || null,
          syncAvailable: false // No external sync needed
        }
      })
    } catch (error: any) {
      console.error('❌ Error checking enrollment status:', error)
      return APIResponse.error(
        'Failed to check enrollment status: ' + error.message,
        500
      )
    }
  }
)
import { NextRequest } from 'next/server'
import { APIResponse, createAPIHandler } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// POST /api/courses/sync-enrollments - Get user enrollments (no external sync needed)
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (_request: NextRequest, { user }: { user: any }) => {
    try {
      // Get user's current enrollments from database
      const enrollments = await prisma.courseEnrollment.findMany({
        where: {
          userId: user.id,
          status: 'active'
        },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              slug: true,
              thumbnailImage: true,
              price: true,
              category: true,
              level: true,
              instructor: {
                select: {
                  id: true,
                  name: true,
                  image: true
                }
              }
            }
          }
        },
        orderBy: { enrolledAt: 'desc' }
      })

      return APIResponse.success({
        message: 'Enrollments retrieved successfully',
        data: {
          enrollments,
          summary: {
            totalEnrollments: enrollments.length,
            syncedEnrollments: enrollments.length,
            lastSyncAt: new Date().toISOString()
          }
        }
      })

          if (course) {
            // Check if enrollment already exists
            const existingEnrollment = await prisma.courseEnrollment.findUnique({
              where: {
                userId_courseId: {
                  userId: user.id,
                  courseId: course.id
                }
              }
            })

            // Update or create enrollment record
            await prisma.courseEnrollment.upsert({
              where: {
                userId_courseId: {
                  userId: user.id,
                  courseId: course.id
                }
              },
              update: {
                progress: enrollment.progress || 0,
                status: enrollment.status === 'completed' ? 'COMPLETED' : 'ACTIVE',
                lastAccessedAt: new Date()
              },
              create: {
                userId: user.id,
                courseId: course.id,
                productId: enrollment.productId,
                enrolledAt: new Date(enrollment.enrolledAt),
                progress: enrollment.progress || 0,
                status: enrollment.status === 'completed' ? 'COMPLETED' : 'ACTIVE'
              }
            })

            if (existingEnrollment) {
              updatedCount++
            } else {
              createdCount++
            }

            syncedCount++
          } else {
            console.warn(`⚠️ Course not found for productId: ${enrollment.productId}`)
          }
        }

        return APIResponse.success({
          message: 'Enrollments synced successfully',
          summary: {
            totalGraphyEnrollments: graphyEnrollments.length,
            syncedEnrollments: syncedCount,
            createdEnrollments: createdCount,
            updatedEnrollments: updatedCount
          },
          enrollments: graphyEnrollments.map(e => ({
            productId: e.productId,
            title: e.title,
            status: e.status,
            progress: e.progress,
            enrolledAt: e.enrolledAt
          }))
        })

      } catch (graphyError: any) {
        console.error('❌ Error fetching from Graphy:', graphyError)

        if (graphyError instanceof GraphyApiError) {
          return APIResponse.error(
            'Failed to sync with Graphy: ' + graphyError.message,
            500
          )
        }

        return APIResponse.error(
          'Failed to sync enrollments from Graphy',
          500
        )
      }

    } catch (error: any) {
      console.error('❌ Error syncing enrollments:', error)
      return APIResponse.error(
        'Failed to sync enrollments: ' + error.message,
        500
      )
    }
  }
)

// GET /api/courses/sync-enrollments - Check sync status
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (_request: NextRequest, { user }: { user: any }) => {
    try {
      // Get user's current enrollments
      const enrollments = await prisma.courseEnrollment.findMany({
        where: { userId: user.id },
        include: {
          course: {
            select: {
              title: true,
              productId: true,
              slug: true
            }
          }
        },
        orderBy: { enrolledAt: 'desc' }
      })

      const summary = {
        totalEnrollments: enrollments.length,
        activeEnrollments: enrollments.filter(e => e.status === 'ACTIVE').length,
        completedEnrollments: enrollments.filter(e => e.status === 'COMPLETED').length,
        pendingEnrollments: enrollments.filter(e => e.status === 'PENDING').length
      }

      return APIResponse.success({
        message: 'Current enrollment status',
        summary,
        enrollments: enrollments.map(e => ({
          id: e.id,
          courseTitle: e.course.title,
          productId: e.course.productId,
          status: e.status,
          progress: e.progress,
          enrolledAt: e.enrolledAt,
          lastAccessedAt: e.lastAccessedAt
        }))
      })

    } catch (error: any) {
      console.error('❌ Error checking sync status:', error)
      return APIResponse.error(
        'Failed to check sync status: ' + error.message,
        500
      )
    }
  }
)
