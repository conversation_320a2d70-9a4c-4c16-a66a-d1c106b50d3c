import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { getBunnyStorageWithTest } from '@/lib/bunny-storage'

// POST /api/admin/upload/files - Upload file attachments
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const formData = await request.formData()
      const file = formData.get('file') as File
      const lessonId = formData.get('lessonId') as string
      const type = formData.get('type') as string || 'attachment'

      if (!file) {
        return APIResponse.error('No file provided', 400)
      }

      // Validate file size (max 50MB)
      const maxSize = 50 * 1024 * 1024 // 50MB
      if (file.size > maxSize) {
        return APIResponse.error('File too large. Maximum size is 50MB', 400)
      }

      // Allowed file types
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'video/mp4',
        'video/webm',
        'audio/mpeg',
        'audio/wav',
        'application/zip',
        'application/x-rar-compressed'
      ]

      if (!allowedTypes.includes(file.type)) {
        return APIResponse.error(`File type ${file.type} is not allowed`, 400)
      }

      // Get Bunny storage instance with connection testing
      const bunnyStorage = await getBunnyStorageWithTest()

      // Generate unique filename
      const timestamp = Date.now()
      const fileExtension = file.name.split('.').pop() || 'bin'
      const uniqueFilename = `${type}-${lessonId || 'general'}-${timestamp}.${fileExtension}`

      // Upload file to Bunny CDN
      const uploadResult = await bunnyStorage.uploadFile(file, {
        folder: `courses/attachments`,
        filename: uniqueFilename,
        maxSize: maxSize
      })

      if (!uploadResult.success) {
        return APIResponse.error(
          `Failed to upload file: ${uploadResult.error}`,
          500
        )
      }

      // Create file object
      const fileData = {
        id: `file-${timestamp}`,
        url: uploadResult.url!,
        filename: uniqueFilename,
        originalName: file.name,
        type: file.type,
        size: uploadResult.size!,
        uploadedAt: new Date().toISOString(),
        lessonId: lessonId || null,
        attachmentType: type
      }

      return APIResponse.success({
        message: 'File uploaded successfully',
        ...fileData
      })

    } catch (error) {
      console.error('Error uploading file:', error)
      return APIResponse.error('Failed to upload file', 500)
    }
  }
)
