'use client'

import { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CloudArrowUpIcon,
  PlayIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'

interface VideoUploadProps {
  lessonId: string
  existingVideo?: {
    id: string
    url: string
    filename: string
    originalName: string
    duration?: number
    size: number
  }
  onUploadComplete: (video: any) => void
  onUploadError: (error: string) => void
}

interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export default function VideoUpload({ 
  lessonId, 
  existingVideo, 
  onUploadComplete, 
  onUploadError 
}: VideoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null)
  const [dragOver, setDragOver] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const allowedTypes = [
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/avi',
    'video/mov',
    'video/wmv',
    'video/flv',
    'video/mkv'
  ]

  const maxFileSize = 100 * 1024 * 1024 // 100MB

  const handleFileSelect = (file: File) => {
    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type. Please select a video file.')
      return
    }

    if (file.size > maxFileSize) {
      toast.error('File size exceeds 100MB limit.')
      return
    }

    setSelectedFile(file)
    
    // Create preview URL
    const url = URL.createObjectURL(file)
    setPreviewUrl(url)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const uploadVideo = async () => {
    if (!selectedFile) return

    try {
      setUploading(true)
      setUploadProgress({ loaded: 0, total: selectedFile.size, percentage: 0 })

      const formData = new FormData()
      formData.append('video', selectedFile)
      formData.append('lessonId', lessonId)
      formData.append('generateThumbnail', 'true')

      const xhr = new XMLHttpRequest()

      // Track upload progress
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const percentage = Math.round((e.loaded / e.total) * 100)
          setUploadProgress({
            loaded: e.loaded,
            total: e.total,
            percentage
          })
        }
      })

      // Handle completion
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText)
          toast.success('Video uploaded successfully!')
          onUploadComplete(response.video)
          setSelectedFile(null)
          setPreviewUrl(null)
          setUploadProgress(null)
        } else {
          const error = JSON.parse(xhr.responseText)
          throw new Error(error.message || 'Upload failed')
        }
      })

      // Handle errors
      xhr.addEventListener('error', () => {
        throw new Error('Upload failed')
      })

      xhr.open('POST', '/api/admin/courses/videos/upload')
      xhr.send(formData)
    } catch (error: any) {
      console.error('Upload error:', error)
      toast.error(error.message || 'Failed to upload video')
      onUploadError(error.message || 'Upload failed')
      setUploadProgress(null)
    } finally {
      setUploading(false)
    }
  }

  const cancelUpload = () => {
    setSelectedFile(null)
    setPreviewUrl(null)
    setUploadProgress(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0:00'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <div className="space-y-6">
      {/* Existing Video */}
      {existingVideo && !selectedFile && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">Current Video</h3>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <PlayIcon className="w-4 h-4" />
              <span>{formatDuration(existingVideo.duration)}</span>
              <span>•</span>
              <span>{formatFileSize(existingVideo.size)}</span>
            </div>
          </div>
          
          <div className="aspect-video bg-gray-100 rounded-xl overflow-hidden mb-4">
            <video
              src={existingVideo.url}
              controls
              className="w-full h-full object-cover"
              preload="metadata"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-800">{existingVideo.originalName}</p>
              <p className="text-sm text-gray-600">{existingVideo.filename}</p>
            </div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => fileInputRef.current?.click()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              Replace Video
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Upload Area */}
      {!existingVideo || selectedFile ? (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
        >
          {!selectedFile ? (
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${
                dragOver
                  ? 'border-blue-500 bg-blue-50/50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <CloudArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Upload Video
              </h3>
              <p className="text-gray-600 mb-4">
                Drag and drop your video file here, or click to browse
              </p>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => fileInputRef.current?.click()}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Choose File
              </motion.button>
              <p className="text-xs text-gray-500 mt-4">
                Supported formats: MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV<br />
                Maximum file size: 100MB
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* File Preview */}
              <div className="flex items-center justify-between p-4 bg-gray-50/50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <PlayIcon className="w-8 h-8 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-800">{selectedFile.name}</p>
                    <p className="text-sm text-gray-600">{formatFileSize(selectedFile.size)}</p>
                  </div>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={cancelUpload}
                  className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                >
                  <XMarkIcon className="w-5 h-5" />
                </motion.button>
              </div>

              {/* Video Preview */}
              {previewUrl && (
                <div className="aspect-video bg-gray-100 rounded-xl overflow-hidden">
                  <video
                    src={previewUrl}
                    controls
                    className="w-full h-full object-cover"
                    preload="metadata"
                  />
                </div>
              )}

              {/* Upload Progress */}
              <AnimatePresence>
                {uploadProgress && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-2"
                  >
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Uploading...</span>
                      <span className="font-medium text-blue-600">
                        {uploadProgress.percentage}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${uploadProgress.percentage}%` }}
                        className="bg-gradient-to-r from-blue-600 to-indigo-600 h-2 rounded-full transition-all duration-300"
                      />
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{formatFileSize(uploadProgress.loaded)} of {formatFileSize(uploadProgress.total)}</span>
                      <span>
                        {uploadProgress.percentage === 100 ? 'Processing...' : 'Uploading...'}
                      </span>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Upload Button */}
              {!uploading && (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={uploadVideo}
                  className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  Upload Video
                </motion.button>
              )}
            </div>
          )}
        </motion.div>
      ) : null}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="video/*"
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  )
}
