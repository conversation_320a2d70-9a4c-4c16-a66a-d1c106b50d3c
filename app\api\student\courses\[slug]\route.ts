import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>andler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/courses/[slug] - Get course details for enrolled student
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { params, user }) => {
    try {
      const resolvedParams = await params
      const courseSlug = resolvedParams?.slug as string

      if (!courseSlug) {
        return APIResponse.error('Course slug is required', 400)
      }

      // Find course by slug or ID (for backward compatibility)
      let course = await prisma.course.findUnique({
        where: {
          slug: courseSlug,
          isActive: true,
          isPublished: true
        },
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true
            }
          },
          sections: {
            where: { isPublished: true },
            orderBy: { order: 'asc' },
            include: {
              chapters: {
                where: { isPublished: true },
                orderBy: { order: 'asc' },
                include: {
                  lessons: {
                    where: { isPublished: true },
                    orderBy: { order: 'asc' },
                    include: {
                      video: {
                        select: {
                          id: true,
                          url: true,
                          duration: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      })

      // If not found by slug, try to find by ID (for backward compatibility)
      if (!course) {
        course = await prisma.course.findUnique({
          where: {
            id: courseSlug,
            isActive: true,
            isPublished: true
          },
          include: {
            instructor: {
              select: {
                id: true,
                name: true,
                image: true
              }
            },
            sections: {
              where: { isPublished: true },
              orderBy: { order: 'asc' },
              include: {
                chapters: {
                  where: { isPublished: true },
                  orderBy: { order: 'asc' },
                  include: {
                    lessons: {
                      where: { isPublished: true },
                      orderBy: { order: 'asc' },
                      include: {
                        video: {
                          select: {
                            id: true,
                            url: true,
                            duration: true
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        })
      }

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Check if user is enrolled in the course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: course.id
          }
        }
      })

      // Allow viewing course details even if not enrolled (for course preview)
      const isEnrolled = enrollment && enrollment.status === 'active'

      // Get student's progress for all lessons if enrolled
      let courseProgress = null
      if (isEnrolled) {
        const lessonIds = course.sections.flatMap(section =>
          section.chapters.flatMap(chapter =>
            chapter.lessons.map(lesson => lesson.id)
          )
        )

        const progress = await prisma.courseProgress.findMany({
          where: {
            userId: user.id,
            lessonId: { in: lessonIds }
          },
          select: {
            id: true,
            lessonId: true,
            isCompleted: true,
            watchTime: true,
            lastPosition: true,
            completedAt: true,
            lastAccessAt: true
          }
        })

        const completedLessons = progress.filter(p => p.isCompleted).length
        const totalWatchTime = progress.reduce((acc, p) => acc + p.watchTime, 0)

        courseProgress = {
          courseId: course.id,
          progress,
          statistics: {
            totalLessons: course.sections.reduce((acc, section) =>
              acc + section.chapters.reduce((chapterAcc, chapter) =>
                chapterAcc + chapter.lessons.length, 0), 0),
            completedLessons,
            totalWatchTime,
            completionPercentage: Math.round((completedLessons / Math.max(1, lessonIds.length)) * 100),
            lastAccessedAt: enrollment?.lastAccessedAt
          }
        }
      }

      // Calculate course statistics
      const totalLessons = course.sections.reduce((acc, section) =>
        acc + section.chapters.reduce((chapterAcc, chapter) =>
          chapterAcc + chapter.lessons.length, 0), 0)

      const totalDuration = course.sections.reduce((acc, section) =>
        acc + section.chapters.reduce((chapterAcc, chapter) =>
          chapterAcc + chapter.lessons.reduce((lessonAcc, lesson) =>
            lessonAcc + (lesson.duration || 0), 0), 0), 0)

      // Filter lessons based on enrollment status and free preview
      const processedSections = course.sections.map(section => ({
        ...section,
        chapters: section.chapters.map(chapter => ({
          ...chapter,
          lessons: chapter.lessons.filter(lesson =>
            // Show all lessons for enrolled users, or only free lessons for non-enrolled
            isEnrolled || lesson.isFree
          )
        }))
      }))

      const courseWithStats = {
        ...course,
        sections: processedSections,
        totalLessons,
        totalDuration: Math.round(totalDuration / 60), // Convert to minutes
        isEnrolled
      }

      return APIResponse.success({
        course: courseWithStats,
        enrollment: isEnrolled ? {
          id: enrollment!.id,
          enrolledAt: enrollment!.enrolledAt,
          progress: enrollment!.progress,
          status: enrollment!.status,
          lastAccessedAt: enrollment!.lastAccessedAt
        } : null,
        courseProgress
      })
    } catch (error) {
      console.error('Error fetching student course:', error)
      return APIResponse.error('Failed to fetch course', 500)
    }
  }
)
