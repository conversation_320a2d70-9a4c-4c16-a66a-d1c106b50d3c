import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// POST /api/debug/resync-test - Test course data (no external sync needed)
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Starting test course data check...')

    // Get existing courses from database for testing
    const courses = await prisma.course.findMany({
      take: 3,
      where: { isActive: true },
      include: {
        instructor: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })
    console.log(`📡 Found ${courses.length} courses in database for testing`)
    
    let updatedCount = 0
    let createdCount = 0
    let errorCount = 0
    const slugChanges: any[] = []
    
    for (const course of graphyCourses) {
      try {
        // Check if course exists in database
        const existingCourse = await prisma.course.findUnique({
          where: { productId: course.productId },
          select: { id: true, slug: true, title: true }
        })
        
        const isUpdate = !!existingCourse
        const slugChanged = existingCourse && existingCourse.slug !== course.slug
        
        if (slugChanged) {
          slugChanges.push({
            productId: course.productId,
            title: course.title,
            oldSlug: existingCourse.slug,
            newSlug: course.slug,
            oldLength: existingCourse.slug?.length || 0,
            newLength: course.slug?.length || 0
          })
        }
        
        console.log(`📝 Processing course: ${course.title}`)
        console.log(`   Product ID: ${course.productId}`)
        console.log(`   Generated slug: "${course.slug}" (${course.slug?.length || 0} chars)`)
        console.log(`   Existing slug: "${existingCourse?.slug || 'N/A'}" (${existingCourse?.slug?.length || 0} chars)`)
        
        // Upsert course with original slug
        await prisma.course.upsert({
          where: { productId: course.productId },
          update: {
            title: course.title,
            description: course.description || null,
            price: course.price,
            slug: course.slug, // Store original slug exactly as received
            thumbnailImage: course.thumbnailImage || null,
            category: course.category || null,
            duration: course.duration || null,
            instructor: course.instructor || null,
            rating: course.rating || null,
            studentsCount: course.studentsCount || null,
            features: course.features || [],
            updatedAt: new Date()
          },
          create: {
            productId: course.productId,
            title: course.title,
            description: course.description || null,
            price: course.price,
            slug: course.slug, // Store original slug exactly as received
            thumbnailImage: course.thumbnailImage || null,
            category: course.category || null,
            duration: course.duration || null,
            instructor: course.instructor || null,
            rating: course.rating || null,
            studentsCount: course.studentsCount || null,
            features: course.features || []
          }
        })
        
        if (isUpdate) {
          updatedCount++
        } else {
          createdCount++
        }
        
        console.log(`✅ ${isUpdate ? 'Updated' : 'Created'} course: ${course.title}`)
        
      } catch (courseError: any) {
        console.error(`❌ Error processing course ${course.productId}:`, courseError.message)
        errorCount++
      }
    }
    
    console.log('🎉 Test course resync completed!')
    
    return APIResponse.success({
      message: 'Test course resync completed successfully',
      summary: {
        totalProcessed: graphyCourses.length,
        created: createdCount,
        updated: updatedCount,
        errors: errorCount,
        slugChanges: slugChanges.length
      },
      slugChanges: slugChanges.length > 0 ? slugChanges : undefined,
      details: {
        note: 'Test resync completed - original Graphy slugs preserved',
        timestamp: new Date().toISOString()
      }
    })
    
  } catch (error: any) {
    console.error('❌ Error during test course resync:', error)
    return APIResponse.error(
      'Failed to test resync courses: ' + error.message,
      500
    )
  }
}

// GET /api/debug/resync-test - Check test resync status
export async function GET(request: NextRequest) {
  try {
    // Get course count and sample slugs
    const totalCourses = await prisma.course.count()
    
    const sampleCourses = await prisma.course.findMany({
      take: 5,
      select: {
        productId: true,
        title: true,
        slug: true,
        updatedAt: true
      },
      orderBy: { updatedAt: 'desc' }
    })
    
    return APIResponse.success({
      message: 'Test resync status',
      summary: {
        totalCourses,
        lastUpdated: sampleCourses[0]?.updatedAt || null
      },
      sampleCourses: sampleCourses.map(c => ({
        productId: c.productId,
        title: c.title,
        slug: c.slug,
        slugLength: c.slug?.length || 0,
        lastUpdated: c.updatedAt
      }))
    })
    
  } catch (error: any) {
    console.error('❌ Error checking test resync status:', error)
    return APIResponse.error(
      'Failed to check test resync status: ' + error.message,
      500
    )
  }
}
