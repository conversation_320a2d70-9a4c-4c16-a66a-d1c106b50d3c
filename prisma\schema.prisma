// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enum for user roles
enum UserRole {
  STUDENT
  ADMIN
}

// Enum for quiz types
enum QuizType {
  QUIZ
  TEST_SERIES
  DAILY_PRACTICE
}

// Enum for question types
enum QuestionType {
  MCQ
  TRUE_FALSE
  SHORT_ANSWER
  MATCHING
}

// Enum for difficulty levels
enum DifficultyLevel {
  EASY
  MEDIUM
  HARD
}

// Enum for lesson types in courses
enum LessonType {
  VIDEO
  TEXT
  QUIZ
  ASSIGNMENT
  DOCUMENT
}

// Enum for notification types
enum NotificationType {
  Q<PERSON>Z_AVAILABLE
  QUIZ_REMINDER
  QUIZ_STARTING
  QUIZ_ENDING
  QUIZ_ENROLLED
  QUIZ_COMPLETED
  RESULT_PUBLISHED
  ACHIEVEMENT_UNLOCKED
  BADGE_EARNED
  RANK_CHANGED
  STREAK_MILESTONE
  ANNOUNCEMENT
  SYSTEM_UPDATE
  MAINTENANCE
  WELCOME
  COURSE_COMPLETED
  DEADLINE_APPROACHING
  FEEDBACK_REQUEST
}

// Enum for live quiz session status
enum LiveQuizStatus {
  WAITING
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

// Auth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime? @map("email_verified")
  image         String?
  role          UserRole  @default(STUDENT)
  bio           String?
  location      String?   // User's location
  website       String?   // User's website URL
  github        String?   // GitHub username
  linkedin      String?   // LinkedIn username
  twitter       String?   // Twitter username
  points        Int       @default(0)
  totalPoints   Int       @default(0) @map("total_points") // Alias for points for compatibility
  level         Int       @default(1)
  totalQuizzes  Int       @default(0) @map("total_quizzes") // Total quizzes taken
  averageScore  Float     @default(0) @map("average_score") // Average quiz score
  streak        Int       @default(0) // Current streak
  totalTimeSpent Int      @default(0) @map("total_time_spent") // Total time spent in seconds
  longestStreak Int       @default(0) @map("longest_streak") // Longest streak achieved
  lastLoginAt   DateTime? @map("last_login_at") // Last login timestamp
  lastActiveAt  DateTime? @map("last_active_at") // Last activity timestamp
  preferences   Json?     // User preferences as JSON
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  accounts      Account[]
  sessions      Session[]
  quizzes       Quiz[]
  quizAttempts  QuizAttempt[]
  achievements  UserAchievement[]
  notifications UserNotification[]
  enrollments   QuizEnrollment[]
  files         File[]
  pdfExports    PdfExport[]
  apiKeys       ApiKey[]
  scheduledQuizzes ScheduledQuiz[]
  practiceStats UserPracticeStats?
  chatMessages  ChatMessage[]
  quizReviews   QuizReview[]
  quizFavorites QuizFavorite[]
  liveQuizSessions LiveQuizSession[]
  liveQuizParticipations LiveQuizParticipant[]
  courseEnrollments CourseEnrollment[]

  // Course management relations
  instructedCourses Course[] @relation("CourseInstructor")
  courseProgress CourseProgress[]
  courseQuizAttempts CourseQuizAttempt[]
  courseDiscussions CourseDiscussion[]
  courseCertificates CourseCertificate[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Application-specific models
model Quiz {
  id            String          @id @default(cuid())
  title         String
  description   String?
  type          QuizType        @default(QUIZ)
  difficulty    DifficultyLevel @default(MEDIUM)
  thumbnail     String?         // URL to thumbnail image
  tags          String[]        // Array of tags for categorization
  timeLimit     Int?            // Time limit in minutes
  startTime     DateTime?       @map("start_time")
  endTime       DateTime?       @map("end_time")
  maxAttempts   Int             @default(1) @map("max_attempts")
  passingScore  Int?            @map("passing_score")
  instructions  String?         // Custom instructions for the quiz
  isPublished   Boolean         @default(false) @map("is_published")
  // Practice session specific fields
  estimatedTime Int?            @map("estimated_time") // Estimated time in minutes for practice
  points        Int?            // Points awarded for completing practice session
  category      String?         // Category for practice sessions (programming, web-dev, etc.)
  // Hierarchical categories
  subjectId     String?         @map("subject_id")
  chapterId     String?         @map("chapter_id")
  topicId       String?         @map("topic_id")
  createdAt     DateTime        @default(now()) @map("created_at")
  updatedAt     DateTime        @updatedAt @map("updated_at")
  createdBy     String          @map("created_by")

  creator     User             @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  subject     Subject?         @relation(fields: [subjectId], references: [id], onDelete: SetNull)
  chapter     Chapter?         @relation(fields: [chapterId], references: [id], onDelete: SetNull)
  topic       Topic?           @relation(fields: [topicId], references: [id], onDelete: SetNull)
  questions   Question[]
  attempts    QuizAttempt[]
  enrollments QuizEnrollment[]
  scheduledQuizzes ScheduledQuiz[]
  reviews     QuizReview[]
  favorites   QuizFavorite[]
  liveSessions LiveQuizSession[]

  @@map("quizzes")
}

model Question {
  id           String       @id @default(cuid())
  quizId       String       @map("quiz_id")
  type         QuestionType @default(MCQ)
  text         String
  options      String[]     // Array of options for MCQ
  correctAnswer String      @map("correct_answer") // Correct answer
  explanation  String?      // Explanation for the answer
  points       Int          @default(1) // Points for correct answer
  difficulty   DifficultyLevel @default(MEDIUM) // Question difficulty
  tags         String[]     @default([]) // Question tags
  image        String?      // URL to question image
  order        Int          // Question order in quiz
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @updatedAt @map("updated_at")

  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("questions")
}

model QuizAttempt {
  id                 String   @id @default(cuid())
  quizId             String   @map("quiz_id")
  userId             String   @map("user_id")
  answers            Json     // User's answers with question IDs
  score              Int
  totalPoints        Int      @map("total_points")
  percentage         Float
  correctAnswers     Int      @default(0) @map("correct_answers")
  incorrectAnswers   Int      @default(0) @map("incorrect_answers")
  unansweredQuestions Int     @default(0) @map("unanswered_questions")
  totalQuestions     Int      @default(0) @map("total_questions")
  timeSpent          Int?     @map("time_spent") // Time spent in seconds
  isCompleted        Boolean  @default(false) @map("is_completed")
  isPaused           Boolean  @default(false) @map("is_paused")
  startedAt          DateTime @default(now()) @map("started_at")
  completedAt        DateTime? @map("completed_at")

  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("quiz_attempts")
}

model ScheduledQuiz {
  id            String   @id @default(cuid())
  quizId        String   @map("quiz_id")
  title         String   // Custom title for the scheduled quiz
  description   String?  // Custom description
  startTime     DateTime @map("start_time")
  endTime       DateTime @map("end_time")
  duration      Int?     // Duration in minutes (overrides quiz default)
  maxAttempts   Int      @default(1) @map("max_attempts")
  isActive      Boolean  @default(true) @map("is_active")
  allowLateSubmission Boolean @default(false) @map("allow_late_submission")
  showResults   Boolean  @default(true) @map("show_results")
  shuffleQuestions Boolean @default(false) @map("shuffle_questions")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  createdBy     String   @map("created_by")

  quiz          Quiz     @relation(fields: [quizId], references: [id], onDelete: Cascade)
  creator       User     @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  enrollments   QuizEnrollment[]

  @@map("scheduled_quizzes")
}

// Quiz enrollment model for tracking student enrollments
model QuizEnrollment {
  id              String   @id @default(cuid())
  quizId          String   @map("quiz_id")
  userId          String   @map("user_id")
  scheduledQuizId String?  @map("scheduled_quiz_id")
  enrolledAt      DateTime @default(now()) @map("enrolled_at")

  quiz          Quiz          @relation(fields: [quizId], references: [id], onDelete: Cascade)
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledQuiz ScheduledQuiz? @relation(fields: [scheduledQuizId], references: [id], onDelete: Cascade)

  @@unique([quizId, userId])
  @@map("quiz_enrollments")
}

// Achievement system
model Achievement {
  id          String @id @default(cuid())
  name        String
  description String
  icon        String
  condition   String // JSON string describing the condition
  points      Int    @default(0)
  createdAt   DateTime @default(now()) @map("created_at")

  userAchievements UserAchievement[]

  @@map("achievements")
}

model UserAchievement {
  id            String   @id @default(cuid())
  userId        String   @map("user_id")
  achievementId String   @map("achievement_id")
  unlockedAt    DateTime @default(now()) @map("unlocked_at")

  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievement Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@map("user_achievements")
}

// Notification system
model Notification {
  id          String           @id @default(cuid())
  type        NotificationType
  title       String
  message     String
  data        Json?            // Additional data for the notification
  actionUrl   String?          @map("action_url") // URL to navigate when clicked
  imageUrl    String?          @map("image_url")  // Optional image for rich notifications
  priority    String           @default("normal") // low, normal, high, urgent
  category    String?          // Category for grouping notifications
  expiresAt   DateTime?        @map("expires_at") // When notification expires
  scheduledAt DateTime?        @map("scheduled_at") // For scheduled notifications
  sentAt      DateTime?        @map("sent_at")    // When notification was sent
  createdAt   DateTime         @default(now()) @map("created_at")
  updatedAt   DateTime         @updatedAt @map("updated_at")

  userNotifications UserNotification[]

  @@map("notifications")
}

model UserNotification {
  id             String    @id @default(cuid())
  userId         String    @map("user_id")
  notificationId String    @map("notification_id")
  isRead         Boolean   @default(false) @map("is_read")
  readAt         DateTime? @map("read_at")
  isClicked      Boolean   @default(false) @map("is_clicked") // Track if user clicked notification
  clickedAt      DateTime? @map("clicked_at")
  isDismissed    Boolean   @default(false) @map("is_dismissed") // User dismissed notification
  dismissedAt    DateTime? @map("dismissed_at")
  deliveredAt    DateTime? @map("delivered_at") // When notification was delivered
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  notification Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)

  @@unique([userId, notificationId])
  @@map("user_notifications")
}

// User practice statistics for daily practice tracking
model UserPracticeStats {
  id              String   @id @default(cuid())
  userId          String   @unique @map("user_id")
  currentStreak   Int      @default(0) @map("current_streak")
  longestStreak   Int      @default(0) @map("longest_streak")
  totalSessions   Int      @default(0) @map("total_sessions")
  totalPoints     Int      @default(0) @map("total_points")
  todayTarget     Int      @default(5) @map("today_target")
  lastPracticeDate DateTime? @map("last_practice_date")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_practice_stats")
}

// Analytics and tracking
model QuizAnalytics {
  id               String   @id @default(cuid())
  quizId           String   @map("quiz_id")
  totalAttempts    Int      @default(0) @map("total_attempts")
  averageScore     Float    @default(0) @map("average_score")
  averageTime      Int      @default(0) @map("average_time") // in seconds
  completionRate   Float    @default(0) @map("completion_rate")
  lastUpdated      DateTime @updatedAt @map("last_updated")

  @@unique([quizId])
  @@map("quiz_analytics")
}

// File uploads tracking
model File {
  id           String   @id @default(cuid())
  filename     String
  originalName String   @map("original_name")
  mimeType     String   @map("mime_type")
  size         Int      // File size in bytes
  url          String   // URL to the uploaded file
  uploadType   String   @default("general") @map("upload_type") // 'image', 'document', 'general'
  folder       String   @default("uploads") // Storage folder
  uploadedById String   @map("uploaded_by_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  uploadedBy User @relation(fields: [uploadedById], references: [id], onDelete: Cascade)

  @@map("files")
}

// PDF Export tracking
model PdfExport {
  id        String   @id @default(cuid())
  type      String   // 'quiz-result', 'analytics', 'certificate', 'bulk'
  filename  String
  size      Int      // File size in bytes
  status    String   @default("pending") // 'pending', 'processing', 'completed', 'failed'
  options   String?  // JSON string of export options
  error     String?  // Error message if failed
  userId    String   @map("user_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("pdf_exports")
}

// API Key management for third-party integrations
model ApiKey {
  id          String    @id @default(cuid())
  name        String    // Human-readable name for the API key
  key         String    @unique // The actual API key (hashed)
  keyPrefix   String    // First few characters for identification
  permissions String[]  // Array of permissions (e.g., ["quizzes:read", "users:write"])
  isActive    Boolean   @default(true)
  lastUsedAt  DateTime? @map("last_used_at")
  expiresAt   DateTime? @map("expires_at")
  userId      String    @map("user_id") // User who created the key
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  usage ApiUsage[]

  @@map("api_keys")
}

// API usage tracking
model ApiUsage {
  id        String   @id @default(cuid())
  apiKeyId  String   @map("api_key_id")
  endpoint  String   // API endpoint called
  method    String   // HTTP method
  statusCode Int     @map("status_code")
  responseTime Int   @map("response_time") // Response time in milliseconds
  ipAddress String  @map("ip_address")
  userAgent String? @map("user_agent")
  createdAt DateTime @default(now()) @map("created_at")

  apiKey ApiKey @relation(fields: [apiKeyId], references: [id], onDelete: Cascade)

  @@map("api_usage")
}

// Categories system for hierarchical organization
model Subject {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  isActive    Boolean   @default(true) @map("is_active")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  chapters    Chapter[]
  quizzes     Quiz[]

  @@map("subjects")
}

model Chapter {
  id          String    @id @default(cuid())
  name        String
  description String?
  subjectId   String    @map("subject_id")
  isActive    Boolean   @default(true) @map("is_active")
  order       Int       @default(0) // For ordering chapters within a subject
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  subject     Subject   @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  topics      Topic[]
  quizzes     Quiz[]

  @@unique([subjectId, name])
  @@map("chapters")
}

model Topic {
  id          String    @id @default(cuid())
  name        String
  description String?
  chapterId   String    @map("chapter_id")
  isActive    Boolean   @default(true) @map("is_active")
  order       Int       @default(0) // For ordering topics within a chapter
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  chapter     Chapter   @relation(fields: [chapterId], references: [id], onDelete: Cascade)
  quizzes     Quiz[]

  @@unique([chapterId, name])
  @@map("topics")
}

// System settings
model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique // e.g., "system.siteName", "email.smtpHost"
  value       String   @db.Text // JSON or string value
  category    String   // e.g., "system", "security", "email"
  description String?  // Human-readable description
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("system_settings")
}

// Chat system
model ChatMessage {
  id        String   @id @default(cuid())
  roomId    String   @map("room_id")
  userId    String   @map("user_id")
  message   String   @db.Text
  type      String   @default("text") // 'text', 'image', 'file'
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([roomId, createdAt])
  @@map("chat_messages")
}

// Quiz Reviews
model QuizReview {
  id        String   @id @default(cuid())
  quizId    String   @map("quiz_id")
  userId    String   @map("user_id")
  rating    Int      // 1-5 stars
  title     String?  // Optional review title
  comment   String?  @db.Text // Review comment
  isPublic  Boolean  @default(true) @map("is_public")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([quizId, userId]) // One review per user per quiz
  @@map("quiz_reviews")
}

// Quiz Favorites
model QuizFavorite {
  id        String   @id @default(cuid())
  quizId    String   @map("quiz_id")
  userId    String   @map("user_id")
  createdAt DateTime @default(now()) @map("created_at")

  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([quizId, userId]) // One favorite per user per quiz
  @@map("quiz_favorites")
}

// Live Quiz Sessions
model LiveQuizSession {
  id              String         @id @default(cuid())
  quizId          String         @map("quiz_id")
  title           String         // Custom title for the live session
  description     String?        // Custom description
  status          LiveQuizStatus @default(WAITING)
  maxParticipants Int?           @map("max_participants") // Maximum number of participants
  currentQuestion Int            @default(0) @map("current_question") // Current question index
  questionTimeLimit Int?         @map("question_time_limit") // Time limit per question in seconds
  autoAdvance     Boolean        @default(true) @map("auto_advance") // Auto advance to next question
  showLeaderboard Boolean        @default(true) @map("show_leaderboard") // Show live leaderboard
  allowLateJoin   Boolean        @default(false) @map("allow_late_join") // Allow joining after start
  startTime       DateTime?      @map("start_time") // Actual start time
  endTime         DateTime?      @map("end_time") // Actual end time
  scheduledStart  DateTime?      @map("scheduled_start") // Scheduled start time
  createdBy       String         @map("created_by")
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @updatedAt @map("updated_at")

  // Relations
  quiz         Quiz                  @relation(fields: [quizId], references: [id], onDelete: Cascade)
  creator      User                  @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  participants LiveQuizParticipant[]

  @@map("live_quiz_sessions")
}

// Live Quiz Participants
model LiveQuizParticipant {
  id              String    @id @default(cuid())
  sessionId       String    @map("session_id")
  userId          String    @map("user_id")
  joinedAt        DateTime  @default(now()) @map("joined_at")
  leftAt          DateTime? @map("left_at")
  currentQuestion Int       @default(0) @map("current_question") // Current question index
  score           Int       @default(0) // Current score
  correctAnswers  Int       @default(0) @map("correct_answers")
  totalAnswered   Int       @default(0) @map("total_answered")
  isActive        Boolean   @default(true) @map("is_active") // Currently active in session
  rank            Int?      // Current rank in leaderboard
  answers         Json      @default("{}") // Answers submitted so far
  timeSpent       Int       @default(0) @map("time_spent") // Total time spent in seconds

  // Relations
  session LiveQuizSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user    User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([sessionId, userId]) // One participation per user per session
  @@map("live_quiz_participants")
}

// Course Management System - Self-hosted course platform
model Course {
  id            String   @id @default(cuid())
  title         String
  description   String?
  shortDescription String? @map("short_description") // Brief summary for cards
  price         Float    @default(0)
  originalPrice Float?   @map("original_price")
  slug          String   @unique
  thumbnailImage String? @map("thumbnail_image")
  category      String?
  level         String?  // Beginner, Intermediate, Advanced
  language      String   @default("en")
  duration      String?  // Estimated completion time
  instructorId  String   @map("instructor_id")
  rating        Float?
  studentsCount Int?     @map("students_count")
  features      String[] @default([])
  tags          String[] @default([])
  requirements  String[] @default([]) // Prerequisites
  whatYouLearn  String[] @default([]) @map("what_you_learn") // Learning outcomes
  isPublished   Boolean  @default(false) @map("is_published")
  isActive      Boolean  @default(true) @map("is_active")
  publishedAt   DateTime? @map("published_at")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  instructor    User                @relation("CourseInstructor", fields: [instructorId], references: [id], onDelete: Cascade)
  sections      CourseSection[]
  enrollments   CourseEnrollment[]
  quizzes       CourseQuiz[]
  discussions   CourseDiscussion[]
  certificates  CourseCertificate[]

  @@map("courses")
}

model CourseSection {
  id          String   @id @default(cuid())
  courseId    String   @map("course_id")
  title       String
  description String?
  order       Int      // For drag-drop ordering
  isPublished Boolean  @default(false) @map("is_published")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  course   Course          @relation(fields: [courseId], references: [id], onDelete: Cascade)
  chapters CourseChapter[]

  @@map("course_sections")
}

model CourseChapter {
  id          String   @id @default(cuid())
  sectionId   String   @map("section_id")
  title       String
  description String?
  order       Int      // For drag-drop ordering
  isPublished Boolean  @default(false) @map("is_published")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  section CourseSection @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  lessons CourseLesson[]

  @@map("course_chapters")
}

model CourseLesson {
  id          String      @id @default(cuid())
  chapterId   String      @map("chapter_id")
  title       String
  description String?
  content     String?     // Text content
  type        LessonType  @default(VIDEO) // VIDEO, TEXT, QUIZ, ASSIGNMENT
  order       Int         // For drag-drop ordering
  duration    Int?        // Duration in seconds
  isPublished Boolean     @default(false) @map("is_published")
  isFree      Boolean     @default(false) @map("is_free") // Preview lessons
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  chapter     CourseChapter      @relation(fields: [chapterId], references: [id], onDelete: Cascade)
  video       CourseVideo?
  progress    CourseProgress[]
  discussions CourseDiscussion[]
  quizzes     CourseQuiz[]

  @@map("course_lessons")
}

model CourseVideo {
  id           String   @id @default(cuid())
  lessonId     String   @unique @map("lesson_id")
  filename     String
  originalName String   @map("original_name")
  url          String   // Bunny CDN URL
  thumbnailUrl String?  @map("thumbnail_url")
  duration     Int?     // Duration in seconds
  size         Int      // File size in bytes
  quality      String?  // Video quality/resolution
  mimeType     String   @map("mime_type")
  uploadedAt   DateTime @default(now()) @map("uploaded_at")

  // Relations
  lesson CourseLesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@map("course_videos")
}

model CourseProgress {
  id             String    @id @default(cuid())
  userId         String    @map("user_id")
  lessonId       String    @map("lesson_id")
  isCompleted    Boolean   @default(false) @map("is_completed")
  watchTime      Int       @default(0) @map("watch_time") // Seconds watched
  lastPosition   Int       @default(0) @map("last_position") // Last video position
  completedAt    DateTime? @map("completed_at")
  firstAccessAt  DateTime  @default(now()) @map("first_access_at")
  lastAccessAt   DateTime  @default(now()) @map("last_access_at")

  // Relations
  user   User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  lesson CourseLesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@unique([userId, lessonId])
  @@map("course_progress")
}

model CourseEnrollment {
  id             String    @id @default(cuid())
  userId         String    @map("user_id")
  courseId       String    @map("course_id")
  enrolledAt     DateTime  @default(now()) @map("enrolled_at")
  progress       Float     @default(0) // Progress percentage (0-100)
  status         String    @default("active") // active, completed, paused
  lastAccessedAt DateTime? @map("last_accessed_at")
  completedAt    DateTime? @map("completed_at")

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("course_enrollments")
}

model CourseQuiz {
  id          String   @id @default(cuid())
  courseId    String   @map("course_id")
  lessonId    String?  @map("lesson_id") // Optional: quiz can be standalone or part of lesson
  title       String
  description String?
  instructions String?
  timeLimit   Int?     // Time limit in minutes
  passingScore Int?    @map("passing_score") // Minimum score to pass
  maxAttempts Int      @default(1) @map("max_attempts")
  order       Int?     // For ordering within course
  isPublished Boolean  @default(false) @map("is_published")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  course    Course              @relation(fields: [courseId], references: [id], onDelete: Cascade)
  lesson    CourseLesson?       @relation(fields: [lessonId], references: [id], onDelete: SetNull)
  questions CourseQuizQuestion[]
  attempts  CourseQuizAttempt[]

  @@map("course_quizzes")
}

model CourseQuizQuestion {
  id            String       @id @default(cuid())
  quizId        String       @map("quiz_id")
  type          QuestionType @default(MCQ)
  text          String
  options       String[]     // Array of options for MCQ
  correctAnswer String       @map("correct_answer")
  explanation   String?      // Explanation for the answer
  points        Int          @default(1)
  order         Int          // Question order in quiz
  createdAt     DateTime     @default(now()) @map("created_at")
  updatedAt     DateTime     @updatedAt @map("updated_at")

  // Relations
  quiz CourseQuiz @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("course_quiz_questions")
}

model CourseQuizAttempt {
  id               String   @id @default(cuid())
  quizId           String   @map("quiz_id")
  userId           String   @map("user_id")
  answers          Json     // User's answers with question IDs
  score            Int
  totalPoints      Int      @map("total_points")
  percentage       Float
  correctAnswers   Int      @default(0) @map("correct_answers")
  incorrectAnswers Int      @default(0) @map("incorrect_answers")
  timeSpent        Int?     @map("time_spent") // Time spent in seconds
  isCompleted      Boolean  @default(false) @map("is_completed")
  startedAt        DateTime @default(now()) @map("started_at")
  completedAt      DateTime? @map("completed_at")

  // Relations
  quiz CourseQuiz @relation(fields: [quizId], references: [id], onDelete: Cascade)
  user User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("course_quiz_attempts")
}

model CourseDiscussion {
  id        String   @id @default(cuid())
  courseId  String   @map("course_id")
  lessonId  String?  @map("lesson_id") // Optional: discussion can be course-wide or lesson-specific
  userId    String   @map("user_id")
  parentId  String?  @map("parent_id") // For threaded discussions
  content   String
  isEdited  Boolean  @default(false) @map("is_edited")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  course   Course             @relation(fields: [courseId], references: [id], onDelete: Cascade)
  lesson   CourseLesson?      @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  user     User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent   CourseDiscussion?  @relation("DiscussionReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies  CourseDiscussion[] @relation("DiscussionReplies")

  @@map("course_discussions")
}

model CourseCertificate {
  id           String   @id @default(cuid())
  courseId     String   @map("course_id")
  userId       String   @map("user_id")
  certificateId String  @unique @map("certificate_id") // Unique verification ID
  issuedAt     DateTime @default(now()) @map("issued_at")
  completionDate DateTime @map("completion_date")
  finalScore   Float?   @map("final_score")
  pdfUrl       String?  @map("pdf_url") // Generated PDF URL

  // Relations
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([courseId, userId])
  @@map("course_certificates")
}
