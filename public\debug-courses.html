<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Courses Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .course-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .course-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 18px;
        }
        .course-field {
            margin: 5px 0;
            padding: 5px;
            background: #fff;
            border-radius: 3px;
        }
        .field-name {
            font-weight: bold;
            color: #666;
            display: inline-block;
            width: 150px;
        }
        .field-value {
            color: #333;
        }
        .null-value {
            color: #999;
            font-style: italic;
        }
        .array-value {
            color: #007bff;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Courses Data Structure</h1>
        <button class="button" onclick="fetchAndAnalyzeCourses()">Fetch & Analyze Courses</button>
        <button class="button" onclick="clearResults()">Clear</button>
    </div>

    <div class="container">
        <h2>📊 Analysis Results</h2>
        <div id="results"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        function log(html) {
            const div = document.createElement('div');
            div.innerHTML = html;
            resultsDiv.appendChild(div);
        }

        function analyzeValue(value) {
            if (value === null || value === undefined) {
                return '<span class="null-value">null/undefined</span>';
            }
            if (Array.isArray(value)) {
                return `<span class="array-value">Array(${value.length}): [${value.join(', ')}]</span>`;
            }
            if (typeof value === 'object') {
                return `<span class="array-value">Object: ${JSON.stringify(value)}</span>`;
            }
            return `<span class="field-value">${value}</span>`;
        }

        async function fetchAndAnalyzeCourses() {
            clearResults();
            log('<h3>🔄 Fetching courses from API...</h3>');

            try {
                const response = await fetch('/api/public/courses?limit=3');
                const result = await response.json();
                
                log(`<h3>✅ API Response Status: ${response.status}</h3>`);
                
                // Handle APIResponse format
                const data = result.data || result;
                const courses = data.courses || [];
                
                log(`<h3>📦 Found ${courses.length} courses</h3>`);
                
                if (courses.length === 0) {
                    log('<p style="color: red;">❌ No courses found! Check API and database.</p>');
                    log('<h4>Raw API Response:</h4>');
                    log('<pre>' + JSON.stringify(result, null, 2) + '</pre>');
                    return;
                }

                // Analyze each course
                courses.forEach((course, index) => {
                    log(`<div class="course-card">
                        <div class="course-title">Course ${index + 1}: ${course.title || 'No Title'}</div>
                        
                        <div class="course-field">
                            <span class="field-name">Product ID:</span>
                            ${analyzeValue(course.productId)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Title:</span>
                            ${analyzeValue(course.title)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Description:</span>
                            ${analyzeValue(course.description)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Price:</span>
                            ${analyzeValue(course.price)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Original Price:</span>
                            ${analyzeValue(course.originalPrice)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Category:</span>
                            ${analyzeValue(course.category)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Slug:</span>
                            ${analyzeValue(course.slug)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Duration:</span>
                            ${analyzeValue(course.duration)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Instructor:</span>
                            ${analyzeValue(course.instructor)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Rating:</span>
                            ${analyzeValue(course.rating)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Students Count:</span>
                            ${analyzeValue(course.studentsCount)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Features:</span>
                            ${analyzeValue(course.features)}
                        </div>
                        
                        <div class="course-field">
                            <span class="field-name">Thumbnail:</span>
                            ${analyzeValue(course.thumbnailImage)}
                        </div>
                    </div>`);
                });

                // Show field availability summary
                const fieldAvailability = {
                    title: 0,
                    description: 0,
                    price: 0,
                    category: 0,
                    duration: 0,
                    instructor: 0,
                    rating: 0,
                    studentsCount: 0,
                    features: 0,
                    thumbnailImage: 0
                };

                courses.forEach(course => {
                    Object.keys(fieldAvailability).forEach(field => {
                        if (course[field] && course[field] !== null && course[field] !== undefined) {
                            if (Array.isArray(course[field]) && course[field].length > 0) {
                                fieldAvailability[field]++;
                            } else if (!Array.isArray(course[field])) {
                                fieldAvailability[field]++;
                            }
                        }
                    });
                });

                log('<h3>📈 Field Availability Summary</h3>');
                log('<div class="course-card">');
                Object.entries(fieldAvailability).forEach(([field, count]) => {
                    const percentage = Math.round((count / courses.length) * 100);
                    const color = percentage > 50 ? 'green' : percentage > 0 ? 'orange' : 'red';
                    log(`<div class="course-field">
                        <span class="field-name">${field}:</span>
                        <span style="color: ${color}">${count}/${courses.length} (${percentage}%)</span>
                    </div>`);
                });
                log('</div>');

                // Show raw data
                log('<h3>🔍 Raw Course Data</h3>');
                log('<pre>' + JSON.stringify(courses, null, 2) + '</pre>');

            } catch (error) {
                log(`<p style="color: red;">❌ Error: ${error.message}</p>`);
                console.error('Error:', error);
            }
        }

        // Auto-run on page load
        window.addEventListener('load', () => {
            setTimeout(fetchAndAnalyzeCourses, 1000);
        });
    </script>
</body>
</html>
