'use client'

import { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { 
  PhotoIcon,
  XMarkIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'

interface ImageUploadProps {
  existingImage?: string
  onImageChange: (imageUrl: string | null) => void
  onImageUpload?: (imageData: any) => void
  aspectRatio?: 'square' | 'video' | 'wide'
  maxSize?: number
  allowedTypes?: string[]
  placeholder?: string
}

export default function ImageUpload({
  existingImage,
  onImageChange,
  onImageUpload,
  aspectRatio = 'video', // 16:9 by default
  maxSize = 5 * 1024 * 1024, // 5MB
  allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  placeholder = 'Upload course thumbnail'
}: ImageUploadProps) {
  const [image, setImage] = useState<string | null>(existingImage || null)
  const [uploading, setUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[21/9]'
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const validateFile = (file: File) => {
    if (!allowedTypes.includes(file.type)) {
      toast.error(`File type ${file.type} is not allowed. Please use JPG, PNG, WebP, or GIF.`)
      return false
    }

    if (file.size > maxSize) {
      toast.error(`File size exceeds ${formatFileSize(maxSize)} limit`)
      return false
    }

    return true
  }

  const uploadImage = async (file: File) => {
    try {
      setUploading(true)

      const formData = new FormData()
      formData.append('image', file)
      formData.append('type', 'thumbnail')
      formData.append('aspectRatio', aspectRatio)

      const response = await fetch('/api/admin/upload/images', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('Failed to upload image')
      }

      const result = await response.json()
      const data = result.data || result

      const imageUrl = data.url || data.imageUrl
      setImage(imageUrl)
      onImageChange(imageUrl)
      
      if (onImageUpload) {
        onImageUpload(data)
      }

      toast.success('Image uploaded successfully!')
      return imageUrl
    } catch (error: any) {
      console.error('Upload error:', error)
      toast.error(error.message || 'Failed to upload image')
      throw error
    } finally {
      setUploading(false)
    }
  }

  const handleFileSelect = async (file: File) => {
    if (!validateFile(file)) return

    try {
      await uploadImage(file)
    } catch (error) {
      // Error already handled in uploadImage
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const removeImage = () => {
    setImage(null)
    onImageChange(null)
    toast.success('Image removed')
  }

  const replaceImage = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-4">
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`relative border-2 border-dashed rounded-xl overflow-hidden transition-all duration-200 ${aspectRatioClasses[aspectRatio]} ${
          dragOver
            ? 'border-blue-500 bg-blue-50/50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
      >
        {image ? (
          // Image Preview
          <div className="relative w-full h-full group">
            <img
              src={image}
              alt="Uploaded image"
              className="w-full h-full object-cover"
            />
            
            {/* Overlay with actions */}
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center space-x-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={replaceImage}
                disabled={uploading}
                className="p-3 bg-white/90 text-gray-800 rounded-lg hover:bg-white transition-colors duration-200"
                title="Replace Image"
              >
                <ArrowPathIcon className="w-5 h-5" />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={removeImage}
                disabled={uploading}
                className="p-3 bg-red-500/90 text-white rounded-lg hover:bg-red-600 transition-colors duration-200"
                title="Remove Image"
              >
                <XMarkIcon className="w-5 h-5" />
              </motion.button>
            </div>

            {/* Upload progress overlay */}
            {uploading && (
              <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                  <p className="text-sm">Uploading...</p>
                </div>
              </div>
            )}
          </div>
        ) : (
          // Upload Area
          <div className="w-full h-full flex flex-col items-center justify-center p-6 text-center">
            <PhotoIcon className="w-12 h-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">
              {placeholder}
            </h3>
            <p className="text-gray-600 mb-4">
              Drag and drop an image here, or click to browse
            </p>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
            >
              {uploading ? 'Uploading...' : 'Choose Image'}
            </motion.button>
            <p className="text-xs text-gray-500 mt-3">
              Max {formatFileSize(maxSize)} • JPG, PNG, WebP, GIF
            </p>
          </div>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={allowedTypes.join(',')}
        onChange={(e) => {
          const file = e.target.files?.[0]
          if (file) handleFileSelect(file)
        }}
        className="hidden"
      />

      {/* Image Info */}
      {image && (
        <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
          <p><strong>Aspect Ratio:</strong> {aspectRatio === 'video' ? '16:9' : aspectRatio === 'square' ? '1:1' : '21:9'}</p>
          <p><strong>Recommended for:</strong> {
            aspectRatio === 'video' ? 'Course thumbnails, video previews' :
            aspectRatio === 'square' ? 'Profile pictures, icons' :
            'Banners, hero images'
          }</p>
        </div>
      )}
    </div>
  )
}
