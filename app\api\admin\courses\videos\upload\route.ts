import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { getBunnyStorage, MAX_FILE_SIZES } from '@/lib/bunny-storage'
import { prisma } from '@/lib/prisma'

// Allowed video formats
const ALLOWED_VIDEO_TYPES = [
  'video/mp4',
  'video/webm',
  'video/ogg',
  'video/avi',
  'video/mov',
  'video/wmv',
  'video/flv',
  'video/mkv'
]

// Maximum video file size (100MB)
const MAX_VIDEO_SIZE = 100 * 1024 * 1024

// POST /api/admin/courses/videos/upload - Upload video for course lesson
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    try {
      // Parse form data
      const formData = await request.formData()
      const file = formData.get('video') as File
      const lessonId = formData.get('lessonId') as string
      const generateThumbnail = formData.get('generateThumbnail') === 'true'

      if (!file) {
        return APIResponse.error('No video file provided', 400)
      }

      if (!lessonId) {
        return APIResponse.error('Lesson ID is required', 400)
      }

      // Validate file type
      if (!ALLOWED_VIDEO_TYPES.includes(file.type)) {
        return APIResponse.error(
          `Invalid file type. Allowed types: ${ALLOWED_VIDEO_TYPES.join(', ')}`,
          400
        )
      }

      // Validate file size
      if (file.size > MAX_VIDEO_SIZE) {
        return APIResponse.error(
          `File size exceeds maximum allowed size of ${MAX_VIDEO_SIZE / (1024 * 1024)}MB`,
          400
        )
      }

      // Verify lesson exists and belongs to a course the user can edit
      const lesson = await prisma.courseLesson.findUnique({
        where: { id: lessonId },
        include: {
          chapter: {
            include: {
              section: {
                include: {
                  course: {
                    select: {
                      id: true,
                      title: true,
                      instructorId: true
                    }
                  }
                }
              }
            }
          },
          video: true // Check if video already exists
        }
      })

      if (!lesson) {
        return APIResponse.error('Lesson not found', 404)
      }

      // Check if user has permission to edit this course
      if (lesson.chapter.section.course.instructorId !== user.id) {
        return APIResponse.error('You do not have permission to edit this course', 403)
      }

      // Get Bunny storage instance
      const bunnyStorage = getBunnyStorage()

      // Generate unique filename
      const fileExtension = file.name.split('.').pop() || 'mp4'
      const timestamp = Date.now()
      const uniqueFilename = `lesson-${lessonId}-${timestamp}.${fileExtension}`

      // Upload video to Bunny CDN
      const uploadResult = await bunnyStorage.uploadFile(file, {
        folder: `courses/${lesson.chapter.section.course.id}/videos`,
        filename: uniqueFilename,
        maxSize: MAX_VIDEO_SIZE
      })

      if (!uploadResult.success) {
        return APIResponse.error(
          `Failed to upload video: ${uploadResult.error}`,
          500
        )
      }

      // Get video duration (this would typically be done with a media processing service)
      // For now, we'll set it to null and let the frontend handle duration detection
      const videoDuration = null

      // Create or update video record
      let videoRecord
      if (lesson.video) {
        // Update existing video
        videoRecord = await prisma.courseVideo.update({
          where: { id: lesson.video.id },
          data: {
            filename: uniqueFilename,
            originalName: file.name,
            url: uploadResult.url!,
            duration: videoDuration,
            size: uploadResult.size!,
            mimeType: file.type,
            uploadedAt: new Date()
          }
        })
      } else {
        // Create new video record
        videoRecord = await prisma.courseVideo.create({
          data: {
            lessonId,
            filename: uniqueFilename,
            originalName: file.name,
            url: uploadResult.url!,
            duration: videoDuration,
            size: uploadResult.size!,
            mimeType: file.type
          }
        })
      }

      // Update lesson duration if video duration is available
      if (videoDuration) {
        await prisma.courseLesson.update({
          where: { id: lessonId },
          data: { duration: videoDuration }
        })
      }

      return APIResponse.success({
        message: 'Video uploaded successfully',
        video: {
          id: videoRecord.id,
          url: videoRecord.url,
          filename: videoRecord.filename,
          originalName: videoRecord.originalName,
          duration: videoRecord.duration,
          size: videoRecord.size,
          mimeType: videoRecord.mimeType,
          uploadedAt: videoRecord.uploadedAt
        },
        lesson: {
          id: lesson.id,
          title: lesson.title,
          course: {
            id: lesson.chapter.section.course.id,
            title: lesson.chapter.section.course.title
          }
        }
      })
    } catch (error) {
      console.error('Error uploading video:', error)
      return APIResponse.error('Failed to upload video', 500)
    }
  }
)

// GET /api/admin/courses/videos/upload - Get upload progress (for future implementation)
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    // This endpoint could be used to check upload progress
    // For now, return a simple response
    return APIResponse.success({
      message: 'Video upload endpoint is ready',
      maxFileSize: MAX_VIDEO_SIZE,
      allowedTypes: ALLOWED_VIDEO_TYPES
    })
  }
)
