'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { 
  ArrowLeftIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  Bars3Icon,
  PlayIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import SectionModal from '@/components/admin/modals/section-modal'
import ChapterModal from '@/components/admin/modals/chapter-modal'
import LessonModal from '@/components/admin/modals/lesson-modal'

interface CourseSection {
  id: string
  title: string
  description?: string
  order: number
  isPublished: boolean
  chapters: CourseChapter[]
  totalChapters: number
  totalLessons: number
  totalDuration: number
}

interface CourseChapter {
  id: string
  title: string
  description?: string
  order: number
  isPublished: boolean
  lessons: CourseLesson[]
}

interface CourseLesson {
  id: string
  title: string
  description?: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  order: number
  duration?: number
  isPublished: boolean
  isFree: boolean
  video?: {
    id: string
    url: string
    duration?: number
  }
}

interface Course {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  category?: string
  level?: string
  thumbnailImage?: string
  isPublished: boolean
  isActive: boolean
  publishedAt?: string
  createdAt: string
  updatedAt: string
  instructor: {
    id: string
    name: string
    email: string
    image?: string
  }
  sections: CourseSection[]
  totalLessons: number
  totalDuration: number
  totalQuizzes: number
  totalQuestions: number
  _count: {
    enrollments: number
    discussions: number
    certificates: number
  }
}

export default function CourseDetailPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params?.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [loading, setLoading] = useState(true)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())

  // Modal states
  const [sectionModalOpen, setSectionModalOpen] = useState(false)
  const [chapterModalOpen, setChapterModalOpen] = useState(false)
  const [lessonModalOpen, setLessonModalOpen] = useState(false)
  const [editingSection, setEditingSection] = useState<any>(null)
  const [editingChapter, setEditingChapter] = useState<any>(null)
  const [editingLesson, setEditingLesson] = useState<any>(null)
  const [selectedSectionId, setSelectedSectionId] = useState<string>('')
  const [selectedChapterId, setSelectedChapterId] = useState<string>('')

  useEffect(() => {
    if (courseId) {
      fetchCourse()
    }
  }, [courseId])

  const fetchCourse = async () => {
    try {
      setLoading(true)
      console.log('Fetching course with ID:', courseId)

      const response = await fetch(`/api/admin/courses/${courseId}`)
      console.log('Response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('API Error:', errorData)
        throw new Error(errorData.message || 'Failed to fetch course')
      }

      const result = await response.json()
      console.log('Raw API response:', result)

      const data = result.data || result // Handle APIResponse format
      const course = data.course || data // Handle nested course object

      console.log('Processed course data:', course)

      if (!course) {
        throw new Error('No course data received from API')
      }

      setCourse(course)

      // Expand all sections by default (with safety check)
      if (course && course.sections && Array.isArray(course.sections)) {
        console.log('Found sections:', course.sections.length)
        const sectionIds = new Set(course.sections.map((s: CourseSection) => s.id as string))
        setExpandedSections(sectionIds)
      } else {
        console.log('No sections found in course data, sections:', course?.sections)
        setExpandedSections(new Set())
      }
    } catch (error) {
      console.error('Error fetching course:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to load course')
      router.push('/admin/courses')
    } finally {
      setLoading(false)
    }
  }

  // Modal handlers
  const openSectionModal = (section?: any) => {
    setEditingSection(section || null)
    setSectionModalOpen(true)
  }

  const openChapterModal = (sectionId: string, chapter?: any) => {
    setSelectedSectionId(sectionId)
    setEditingChapter(chapter || null)
    setChapterModalOpen(true)
  }

  const openLessonModal = (sectionId: string, chapterId: string, lesson?: any) => {
    setSelectedSectionId(sectionId)
    setSelectedChapterId(chapterId)
    setEditingLesson(lesson || null)
    setLessonModalOpen(true)
  }

  const closeModals = () => {
    setSectionModalOpen(false)
    setChapterModalOpen(false)
    setLessonModalOpen(false)
    setEditingSection(null)
    setEditingChapter(null)
    setEditingLesson(null)
    setSelectedSectionId('')
    setSelectedChapterId('')
  }

  const handleModalSuccess = () => {
    fetchCourse() // Refresh course data
  }

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const togglePublishStatus = async () => {
    if (!course) return

    try {
      const response = await fetch(`/api/admin/courses/${courseId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isPublished: !course.isPublished })
      })

      if (!response.ok) throw new Error('Failed to update course')

      const data = await response.json()
      toast.success(data.message)
      fetchCourse()
    } catch (error) {
      console.error('Error updating course:', error)
      toast.error('Failed to update course')
    }
  }

  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <PlayIcon className="w-4 h-4" />
      case 'TEXT':
        return <DocumentTextIcon className="w-4 h-4" />
      case 'QUIZ':
        return <AcademicCapIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0m'
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading course...</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Course not found</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Course not found</h2>
          <p className="text-gray-600 mb-6">The course you're looking for doesn't exist.</p>
          <Link href="/admin/courses">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Back to Courses
            </motion.button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/admin/courses">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                >
                  <ArrowLeftIcon className="w-5 h-5" />
                </motion.button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">{course.title}</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    course.isPublished
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {course.isPublished ? 'Published' : 'Draft'}
                  </span>
                  <span>{course.totalLessons} lessons</span>
                  <span>{formatDuration(course.totalDuration * 60)}</span>
                  <span>{course._count.enrollments} students</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Link href={`/admin/courses/${courseId}/edit`}>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                >
                  <PencilIcon className="w-4 h-4 mr-2 inline" />
                  Edit
                </motion.button>
              </Link>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={togglePublishStatus}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  course.isPublished
                    ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                    : 'bg-green-100 text-green-700 hover:bg-green-200'
                }`}
              >
                {course.isPublished ? 'Unpublish' : 'Publish'}
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => openSectionModal()}
                className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <PlusIcon className="w-4 h-4 mr-2 inline" />
                Add Section
              </motion.button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Course Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
            <div className="text-2xl font-bold text-blue-600">{course.totalLessons}</div>
            <div className="text-sm text-gray-600">Total Lessons</div>
          </div>
          <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
            <div className="text-2xl font-bold text-green-600">{course._count.enrollments}</div>
            <div className="text-sm text-gray-600">Enrolled Students</div>
          </div>
          <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
            <div className="text-2xl font-bold text-purple-600">{formatDuration(course.totalDuration * 60)}</div>
            <div className="text-sm text-gray-600">Total Duration</div>
          </div>
          <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
            <div className="text-2xl font-bold text-orange-600">{course.totalQuizzes}</div>
            <div className="text-sm text-gray-600">Quizzes</div>
          </div>
        </div>

        {/* Course Content */}
        <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-800">Course Content</h2>
            <div className="text-sm text-gray-600">
              {course.sections?.length || 0} sections • {course.totalLessons || 0} lessons
            </div>
          </div>

          {!course.sections || course.sections.length === 0 ? (
            <div className="text-center py-12">
              <AcademicCapIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No content yet</h3>
              <p className="text-gray-500 mb-6">Start building your course by adding sections</p>
              <Link href={`/admin/courses/${courseId}/sections/create`}>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <PlusIcon className="w-5 h-5 mr-2 inline" />
                  Add First Section
                </motion.button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {(course.sections || []).map((section, sectionIndex) => (
                <SectionCard
                  key={section.id}
                  section={section}
                  courseId={courseId}
                  isExpanded={expandedSections.has(section.id)}
                  onToggle={() => toggleSection(section.id)}
                  onEditSection={openSectionModal}
                  onAddChapter={(sectionId) => openChapterModal(sectionId)}
                  onEditChapter={(sectionId, chapter) => openChapterModal(sectionId, chapter)}
                  onAddLesson={(sectionId, chapterId) => openLessonModal(sectionId, chapterId)}
                  onEditLesson={(sectionId, chapterId, lesson) => openLessonModal(sectionId, chapterId, lesson)}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <SectionModal
        isOpen={sectionModalOpen}
        onClose={closeModals}
        courseId={courseId}
        onSuccess={handleModalSuccess}
        editingSection={editingSection}
      />

      <ChapterModal
        isOpen={chapterModalOpen}
        onClose={closeModals}
        courseId={courseId}
        sectionId={selectedSectionId}
        onSuccess={handleModalSuccess}
        editingChapter={editingChapter}
      />

      <LessonModal
        isOpen={lessonModalOpen}
        onClose={closeModals}
        courseId={courseId}
        sectionId={selectedSectionId}
        chapterId={selectedChapterId}
        onSuccess={handleModalSuccess}
        editingLesson={editingLesson}
      />
    </div>
  )
}

interface SectionCardProps {
  section: CourseSection
  courseId: string
  isExpanded: boolean
  onToggle: () => void
  onEditSection: (section: any) => void
  onAddChapter: (sectionId: string) => void
  onEditChapter: (sectionId: string, chapter: any) => void
  onAddLesson: (sectionId: string, chapterId: string) => void
  onEditLesson: (sectionId: string, chapterId: string, lesson: any) => void
}

function SectionCard({
  section,
  courseId,
  isExpanded,
  onToggle,
  onEditSection,
  onAddChapter,
  onEditChapter,
  onAddLesson,
  onEditLesson
}: SectionCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="border border-gray-200 rounded-xl overflow-hidden"
    >
      {/* Section Header */}
      <div className="bg-gray-50/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onToggle}
              className="p-1 text-gray-500 hover:text-gray-700 transition-colors duration-200"
            >
              {isExpanded ? (
                <ChevronDownIcon className="w-5 h-5" />
              ) : (
                <ChevronRightIcon className="w-5 h-5" />
              )}
            </motion.button>
            <div>
              <h3 className="font-semibold text-gray-800">{section.title}</h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                <span>{section.totalLessons} lessons</span>
                <span>{Math.round(section.totalDuration / 60)}m</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  section.isPublished
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {section.isPublished ? 'Published' : 'Draft'}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Bars3Icon className="w-4 h-4 text-gray-400 cursor-move" />
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onEditSection(section)}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="Edit Section"
            >
              <PencilIcon className="w-4 h-4" />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
              title="Delete Section"
            >
              <TrashIcon className="w-4 h-4" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Section Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-4 space-y-3">
              {section.chapters.length === 0 ? (
                <div className="text-center py-8">
                  <DocumentTextIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 mb-4">No chapters in this section</p>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => onAddChapter(section.id)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                  >
                    <PlusIcon className="w-4 h-4 mr-2 inline" />
                    Add Chapter
                  </motion.button>
                </div>
              ) : (
                section.chapters.map((chapter, chapterIndex) => (
                  <ChapterCard
                    key={chapter.id}
                    chapter={chapter}
                    courseId={courseId}
                    sectionId={section.id}
                    onEditChapter={onEditChapter}
                    onAddLesson={onAddLesson}
                    onEditLesson={onEditLesson}
                  />
                ))
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

interface ChapterCardProps {
  chapter: CourseChapter
  courseId: string
  sectionId: string
  onEditChapter: (sectionId: string, chapter: any) => void
  onAddLesson: (sectionId: string, chapterId: string) => void
  onEditLesson: (sectionId: string, chapterId: string, lesson: any) => void
}

function ChapterCard({
  chapter,
  courseId,
  sectionId,
  onEditChapter,
  onAddLesson,
  onEditLesson
}: ChapterCardProps) {
  return (
    <div className="bg-white/50 border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <Bars3Icon className="w-4 h-4 text-gray-400 cursor-move" />
          <div>
            <h4 className="font-medium text-gray-800">{chapter.title}</h4>
            <div className="flex items-center space-x-3 text-sm text-gray-600 mt-1">
              <span>{chapter.lessons.length} lessons</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                chapter.isPublished
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {chapter.isPublished ? 'Published' : 'Draft'}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onAddLesson(sectionId, chapter.id)}
            className="p-1 text-blue-600 hover:bg-blue-50 rounded transition-colors duration-200"
            title="Add Lesson"
          >
            <PlusIcon className="w-4 h-4" />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onEditChapter(sectionId, chapter)}
            className="p-1 text-gray-600 hover:bg-gray-50 rounded transition-colors duration-200"
            title="Edit Chapter"
          >
            <PencilIcon className="w-4 h-4" />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors duration-200"
            title="Delete Chapter"
          >
            <TrashIcon className="w-4 h-4" />
          </motion.button>
        </div>
      </div>

      {/* Lessons */}
      {chapter.lessons.length > 0 && (
        <div className="space-y-2 ml-7">
          {chapter.lessons.map((lesson, lessonIndex) => (
            <LessonCard
              key={lesson.id}
              lesson={lesson}
              courseId={courseId}
              sectionId={sectionId}
              chapterId={chapter.id}
              onEditLesson={onEditLesson}
            />
          ))}
        </div>
      )}
    </div>
  )
}

interface LessonCardProps {
  lesson: CourseLesson
  courseId: string
  sectionId: string
  chapterId: string
  onEditLesson: (sectionId: string, chapterId: string, lesson: any) => void
}

function LessonCard({ lesson, courseId, sectionId, chapterId, onEditLesson }: LessonCardProps) {
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0m'
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m`
  }

  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <PlayIcon className="w-4 h-4" />
      case 'TEXT':
        return <DocumentTextIcon className="w-4 h-4" />
      case 'QUIZ':
        return <AcademicCapIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  return (
    <div className="flex items-center justify-between p-3 bg-white/70 border border-gray-100 rounded-lg hover:shadow-sm transition-all duration-200">
      <div className="flex items-center space-x-3">
        <Bars3Icon className="w-3 h-3 text-gray-400 cursor-move" />
        <div className="text-gray-600">
          {getLessonIcon(lesson.type)}
        </div>
        <div>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-800">{lesson.title}</span>
            {lesson.isFree && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Free
              </span>
            )}
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              lesson.isPublished
                ? 'bg-green-100 text-green-800'
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {lesson.isPublished ? 'Published' : 'Draft'}
            </span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {lesson.type} • {formatDuration(lesson.duration)}
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => onEditLesson(sectionId, chapterId, lesson)}
          className="p-1 text-gray-600 hover:bg-gray-50 rounded transition-colors duration-200"
          title="Edit Lesson"
        >
          <PencilIcon className="w-3 h-3" />
        </motion.button>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors duration-200"
          title="Delete Lesson"
        >
          <TrashIcon className="w-3 h-3" />
        </motion.button>
      </div>
    </div>
  )
}
