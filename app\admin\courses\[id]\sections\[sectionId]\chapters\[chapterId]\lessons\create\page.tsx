'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useParams, useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { 
  ArrowLeftIcon,
  CloudArrowUpIcon,
  PlayIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface LessonFormData {
  title: string
  description: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  order: number
  duration: number
  isPublished: boolean
  isFree: boolean
  content: string
  videoUrl: string
}

export default function CreateLessonPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params?.id as string
  const sectionId = params?.sectionId as string
  const chapterId = params?.chapterId as string

  const [loading, setLoading] = useState(false)
  const [uploadingVideo, setUploadingVideo] = useState(false)
  const [formData, setFormData] = useState<LessonFormData>({
    title: '',
    description: '',
    type: 'VIDEO',
    order: 1,
    duration: 0,
    isPublished: false,
    isFree: false,
    content: '',
    videoUrl: ''
  })

  const lessonTypes = [
    { value: 'VIDEO', label: 'Video Lesson', icon: PlayIcon },
    { value: 'TEXT', label: 'Text Content', icon: DocumentTextIcon },
    { value: 'QUIZ', label: 'Quiz', icon: AcademicCapIcon },
    { value: 'ASSIGNMENT', label: 'Assignment', icon: ClipboardDocumentListIcon },
    { value: 'DOCUMENT', label: 'Document', icon: DocumentTextIcon }
  ]

  const handleInputChange = (field: keyof LessonFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleVideoUpload = async (file: File) => {
    if (!file) return

    try {
      setUploadingVideo(true)
      
      // Create FormData for file upload
      const uploadFormData = new FormData()
      uploadFormData.append('video', file)
      uploadFormData.append('courseId', courseId)
      uploadFormData.append('lessonTitle', formData.title || 'Untitled Lesson')

      const response = await fetch('/api/admin/upload/video', {
        method: 'POST',
        body: uploadFormData
      })

      if (!response.ok) {
        throw new Error('Failed to upload video')
      }

      const result = await response.json()
      const data = result.data || result

      // Update form with video URL and duration
      setFormData(prev => ({
        ...prev,
        videoUrl: data.videoUrl || data.url,
        duration: data.duration || 0
      }))

      toast.success('Video uploaded successfully!')
    } catch (error: any) {
      console.error('Error uploading video:', error)
      toast.error(error.message || 'Failed to upload video')
    } finally {
      setUploadingVideo(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast.error('Lesson title is required')
      return
    }

    if (formData.type === 'VIDEO' && !formData.videoUrl) {
      toast.error('Please upload a video for video lessons')
      return
    }

    try {
      setLoading(true)
      console.log('Creating lesson:', formData)
      
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        const error = await response.json()
        console.error('API Error:', error)
        throw new Error(error.message || 'Failed to create lesson')
      }

      const result = await response.json()
      console.log('API Response:', result)
      
      const data = result.data || result
      toast.success(data.message || 'Lesson created successfully!')
      
      // Navigate back to course detail page
      router.push(`/admin/courses/${courseId}`)

    } catch (error: any) {
      console.error('Error creating lesson:', error)
      toast.error(error.message || 'Failed to create lesson')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href={`/admin/courses/${courseId}`}>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </motion.button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Create New Lesson
            </h1>
            <p className="text-gray-600 mt-2">Add a new lesson to this chapter</p>
          </div>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Lesson Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lesson Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder="Enter lesson title"
                required
              />
            </div>

            {/* Lesson Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Lesson Type *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {lessonTypes.map((type) => {
                  const Icon = type.icon
                  return (
                    <motion.button
                      key={type.value}
                      type="button"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleInputChange('type', type.value)}
                      className={`p-4 border-2 rounded-xl transition-all duration-200 ${
                        formData.type === type.value
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 bg-white/50 text-gray-600 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="w-6 h-6 mx-auto mb-2" />
                      <div className="text-sm font-medium">{type.label}</div>
                    </motion.button>
                  )
                })}
              </div>
            </div>

            {/* Video Upload (only for VIDEO type) */}
            {formData.type === 'VIDEO' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Video Upload *
                </label>
                {!formData.videoUrl ? (
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center">
                    <CloudArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <div className="text-gray-600 mb-4">
                      <p className="text-lg font-medium">Upload your video</p>
                      <p className="text-sm">Drag and drop or click to browse</p>
                    </div>
                    <input
                      type="file"
                      accept="video/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) handleVideoUpload(file)
                      }}
                      className="hidden"
                      id="video-upload"
                      disabled={uploadingVideo}
                    />
                    <label
                      htmlFor="video-upload"
                      className={`inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-medium cursor-pointer hover:bg-blue-700 transition-colors duration-200 ${
                        uploadingVideo ? 'opacity-50 cursor-not-allowed' : ''
                      }`}
                    >
                      {uploadingVideo ? 'Uploading...' : 'Choose Video File'}
                    </label>
                  </div>
                ) : (
                  <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                    <div className="flex items-center space-x-3">
                      <PlayIcon className="w-6 h-6 text-green-600" />
                      <div>
                        <p className="text-green-800 font-medium">Video uploaded successfully</p>
                        <p className="text-green-600 text-sm">Duration: {Math.round(formData.duration / 60)} minutes</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Lesson Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                placeholder="Describe what this lesson covers"
              />
            </div>

            {/* Content (for non-video lessons) */}
            {formData.type !== 'VIDEO' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  rows={8}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                  placeholder="Enter lesson content..."
                />
              </div>
            )}

            {/* Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Order */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Order
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.order}
                  onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 1)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="1"
                />
              </div>

              {/* Duration (for non-video lessons) */}
              {formData.type !== 'VIDEO' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (minutes)
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={Math.round(formData.duration / 60)}
                    onChange={(e) => handleInputChange('duration', (parseInt(e.target.value) || 0) * 60)}
                    className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="0"
                  />
                </div>
              )}
            </div>

            {/* Checkboxes */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="isPublished"
                  checked={formData.isPublished}
                  onChange={(e) => handleInputChange('isPublished', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <label htmlFor="isPublished" className="text-sm font-medium text-gray-700">
                  Publish lesson immediately
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="isFree"
                  checked={formData.isFree}
                  onChange={(e) => handleInputChange('isFree', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <label htmlFor="isFree" className="text-sm font-medium text-gray-700">
                  Make this lesson free (preview)
                </label>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link href={`/admin/courses/${courseId}`}>
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200"
                >
                  Cancel
                </motion.button>
              </Link>
              <motion.button
                type="submit"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                disabled={loading || uploadingVideo}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Lesson'}
              </motion.button>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  )
}
