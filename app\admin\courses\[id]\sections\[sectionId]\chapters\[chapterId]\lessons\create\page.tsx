'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useParams, useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import {
  ArrowLeftIcon,
  PlayIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ClipboardDocumentListIcon,
  DocumentIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import VideoUpload from '@/components/admin/video-upload'

interface LessonFormData {
  title: string
  description: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  order: number
  duration: number
  isPublished: boolean
  isFree: boolean
  content: string
  videoUrl: string
}

export default function CreateLessonPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params?.id as string
  const sectionId = params?.sectionId as string
  const chapterId = params?.chapterId as string

  const [loading, setLoading] = useState(false)
  const [videoData, setVideoData] = useState<any>(null)
  const [createdLessonId, setCreatedLessonId] = useState<string | null>(null)
  const [showVideoUpload, setShowVideoUpload] = useState(false)
  const [formData, setFormData] = useState<LessonFormData>({
    title: '',
    description: '',
    type: 'VIDEO',
    order: 1,
    duration: 0,
    isPublished: false,
    isFree: false,
    content: '',
    videoUrl: ''
  })

  const lessonTypes = [
    {
      value: 'VIDEO',
      label: 'Video Lesson',
      icon: PlayIcon,
      description: 'Upload and stream video content'
    },
    {
      value: 'TEXT',
      label: 'Text Content',
      icon: DocumentTextIcon,
      description: 'Rich text content and articles'
    },
    {
      value: 'QUIZ',
      label: 'Quiz',
      icon: AcademicCapIcon,
      description: 'Interactive quizzes and assessments'
    },
    {
      value: 'ASSIGNMENT',
      label: 'Assignment',
      icon: ClipboardDocumentListIcon,
      description: 'Homework and practical exercises'
    },
    {
      value: 'DOCUMENT',
      label: 'Document',
      icon: DocumentIcon,
      description: 'PDFs, slides, and downloadable files'
    }
  ]

  const handleInputChange = (field: keyof LessonFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleVideoUploadComplete = (video: any) => {
    console.log('Video upload completed:', video)
    setVideoData(video)
    toast.success('Video uploaded successfully! Lesson is now complete.')

    // Navigate back to course detail page after successful video upload
    setTimeout(() => {
      router.push(`/admin/courses/${courseId}`)
    }, 2000)
  }

  const handleVideoUploadError = (error: string) => {
    console.error('Video upload error:', error)
    toast.error(error)
  }

  const handleSkipVideoUpload = () => {
    toast.info('You can upload the video later from the lesson edit page')
    router.push(`/admin/courses/${courseId}`)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast.error('Lesson title is required')
      return
    }

    // Type-specific validation (video upload will be handled after lesson creation)
    // Video lessons don't require video upload during creation

    if ((formData.type === 'TEXT' || formData.type === 'DOCUMENT') && !formData.content.trim()) {
      toast.error(`Please add content for ${formData.type.toLowerCase()} lessons`)
      return
    }

    if (formData.type === 'QUIZ' && !formData.content.trim()) {
      toast.error('Please add quiz questions and answers')
      return
    }

    if (formData.type === 'ASSIGNMENT' && !formData.content.trim()) {
      toast.error('Please add assignment instructions')
      return
    }

    try {
      setLoading(true)
      console.log('Creating lesson:', formData)
      
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}/lessons`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        const error = await response.json()
        console.error('API Error:', error)
        throw new Error(error.message || 'Failed to create lesson')
      }

      const result = await response.json()
      console.log('API Response:', result)

      const data = result.data || result
      const lesson = data.lesson || data

      toast.success(data.message || 'Lesson created successfully!')

      // If it's a video lesson, show video upload interface
      if (formData.type === 'VIDEO' && lesson?.id) {
        setCreatedLessonId(lesson.id)
        setShowVideoUpload(true)
        toast.info('Now upload your video for this lesson')
      } else {
        // Navigate back to course detail page for non-video lessons
        router.push(`/admin/courses/${courseId}`)
      }

    } catch (error: any) {
      console.error('Error creating lesson:', error)
      toast.error(error.message || 'Failed to create lesson')
    } finally {
      setLoading(false)
    }
  }

  // Show video upload interface after lesson creation
  if (showVideoUpload && createdLessonId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
        <div className="max-w-3xl mx-auto">
          {/* Header */}
          <div className="flex items-center space-x-4 mb-8">
            <Link href={`/admin/courses/${courseId}`}>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 text-gray-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
              >
                <ArrowLeftIcon className="w-5 h-5" />
              </motion.button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Upload Video for "{formData.title}"
              </h1>
              <p className="text-gray-600 mt-2">Lesson created successfully! Now upload your video.</p>
            </div>
          </div>

          {/* Video Upload */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8"
          >
            <VideoUpload
              lessonId={createdLessonId}
              onUploadComplete={handleVideoUploadComplete}
              onUploadError={handleVideoUploadError}
            />

            <div className="flex items-center justify-center mt-6">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleSkipVideoUpload}
                className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200"
              >
                Skip for Now (Upload Later)
              </motion.button>
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
      <div className="max-w-3xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href={`/admin/courses/${courseId}`}>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </motion.button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Create New Lesson
            </h1>
            <p className="text-gray-600 mt-2">Add a new lesson to this chapter</p>
          </div>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Lesson Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lesson Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder="Enter lesson title"
                required
              />
            </div>

            {/* Lesson Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Lesson Type *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {lessonTypes.map((type) => {
                  const Icon = type.icon
                  return (
                    <motion.button
                      key={type.value}
                      type="button"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleInputChange('type', type.value)}
                      className={`p-4 border-2 rounded-xl transition-all duration-200 text-left ${
                        formData.type === type.value
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 bg-white/50 text-gray-600 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="w-6 h-6 mb-3" />
                      <div className="text-sm font-medium mb-1">{type.label}</div>
                      <div className="text-xs text-gray-500">{type.description}</div>
                    </motion.button>
                  )
                })}
              </div>
            </div>

            {/* Video Upload Note (for VIDEO type) */}
            {formData.type === 'VIDEO' && (
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <PlayIcon className="w-6 h-6 text-blue-600" />
                  <div>
                    <p className="text-blue-800 font-medium">Video Upload</p>
                    <p className="text-blue-600 text-sm">
                      After creating the lesson, you'll be able to upload your video file.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Lesson Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                placeholder="Describe what this lesson covers"
              />
            </div>

            {/* Content (for non-video lessons) */}
            {formData.type !== 'VIDEO' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {formData.type === 'TEXT' && 'Text Content *'}
                  {formData.type === 'QUIZ' && 'Quiz Questions & Answers *'}
                  {formData.type === 'ASSIGNMENT' && 'Assignment Instructions *'}
                  {formData.type === 'DOCUMENT' && 'Document Description *'}
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  rows={formData.type === 'QUIZ' ? 12 : 8}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                  placeholder={
                    formData.type === 'TEXT' ? 'Write your lesson content using markdown or plain text...' :
                    formData.type === 'QUIZ' ? 'Add your quiz questions and multiple choice answers...' :
                    formData.type === 'ASSIGNMENT' ? 'Provide clear instructions for the assignment...' :
                    formData.type === 'DOCUMENT' ? 'Describe the document and provide download links...' :
                    'Enter lesson content...'
                  }
                />
                {formData.type === 'QUIZ' && (
                  <p className="text-sm text-gray-500 mt-2">
                    Format: Question? A) Option 1 B) Option 2 C) Option 3 D) Option 4 [Answer: A]
                  </p>
                )}
                {formData.type === 'DOCUMENT' && (
                  <p className="text-sm text-gray-500 mt-2">
                    You can include download links and file descriptions here.
                  </p>
                )}
              </div>
            )}

            {/* Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Order */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Order
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.order}
                  onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 1)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="1"
                />
              </div>

              {/* Duration (for non-video lessons) */}
              {formData.type !== 'VIDEO' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (minutes)
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={Math.round(formData.duration / 60)}
                    onChange={(e) => handleInputChange('duration', (parseInt(e.target.value) || 0) * 60)}
                    className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="0"
                  />
                </div>
              )}
            </div>

            {/* Checkboxes */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="isPublished"
                  checked={formData.isPublished}
                  onChange={(e) => handleInputChange('isPublished', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <label htmlFor="isPublished" className="text-sm font-medium text-gray-700">
                  Publish lesson immediately
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="isFree"
                  checked={formData.isFree}
                  onChange={(e) => handleInputChange('isFree', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <label htmlFor="isFree" className="text-sm font-medium text-gray-700">
                  Make this lesson free (preview)
                </label>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link href={`/admin/courses/${courseId}`}>
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200"
                >
                  Cancel
                </motion.button>
              </Link>
              <motion.button
                type="submit"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                disabled={loading}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Lesson'}
              </motion.button>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  )
}
