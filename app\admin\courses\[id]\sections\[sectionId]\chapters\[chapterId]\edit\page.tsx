'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useParams, useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

interface ChapterFormData {
  title: string
  description: string
  order: number
  isPublished: boolean
}

export default function EditChapterPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params?.id as string
  const sectionId = params?.sectionId as string
  const chapterId = params?.chapterId as string

  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [formData, setFormData] = useState<ChapterFormData>({
    title: '',
    description: '',
    order: 1,
    isPublished: false
  })

  useEffect(() => {
    if (courseId && sectionId && chapterId) {
      fetchChapter()
    }
  }, [courseId, sectionId, chapterId])

  const fetchChapter = async () => {
    try {
      setInitialLoading(true)
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch chapter')
      }

      const result = await response.json()
      const data = result.data || result
      const chapter = data.chapter || data

      setFormData({
        title: chapter.title || '',
        description: chapter.description || '',
        order: chapter.order || 1,
        isPublished: chapter.isPublished || false
      })
    } catch (error) {
      console.error('Error fetching chapter:', error)
      toast.error('Failed to load chapter data')
      router.push(`/admin/courses/${courseId}`)
    } finally {
      setInitialLoading(false)
    }
  }

  const handleInputChange = (field: keyof ChapterFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast.error('Chapter title is required')
      return
    }

    try {
      setLoading(true)
      console.log('Updating chapter:', formData)
      
      const response = await fetch(`/api/admin/courses/${courseId}/sections/${sectionId}/chapters/${chapterId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        const error = await response.json()
        console.error('API Error:', error)
        throw new Error(error.message || 'Failed to update chapter')
      }

      const result = await response.json()
      console.log('API Response:', result)
      
      const data = result.data || result
      toast.success(data.message || 'Chapter updated successfully!')
      
      // Navigate back to course detail page
      router.push(`/admin/courses/${courseId}`)

    } catch (error: any) {
      console.error('Error updating chapter:', error)
      toast.error(error.message || 'Failed to update chapter')
    } finally {
      setLoading(false)
    }
  }

  if (initialLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading chapter data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href={`/admin/courses/${courseId}`}>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </motion.button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Edit Chapter
            </h1>
            <p className="text-gray-600 mt-2">Update chapter information and settings</p>
          </div>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Chapter Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chapter Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder="Enter chapter title"
                required
              />
            </div>

            {/* Chapter Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                placeholder="Describe what this chapter covers"
              />
            </div>

            {/* Order */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Order
              </label>
              <input
                type="number"
                min="1"
                value={formData.order}
                onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 1)}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder="1"
              />
              <p className="text-sm text-gray-500 mt-1">
                The order in which this chapter appears in the section
              </p>
            </div>

            {/* Published Status */}
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="isPublished"
                checked={formData.isPublished}
                onChange={(e) => handleInputChange('isPublished', e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              />
              <label htmlFor="isPublished" className="text-sm font-medium text-gray-700">
                Chapter is published
              </label>
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link href={`/admin/courses/${courseId}`}>
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200"
                >
                  Cancel
                </motion.button>
              </Link>
              <motion.button
                type="submit"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                disabled={loading}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
              >
                {loading ? 'Updating...' : 'Update Chapter'}
              </motion.button>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  )
}
