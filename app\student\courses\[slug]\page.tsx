'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { 
  PlayIcon,
  PauseIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  AcademicCapIcon,
  DocumentTextIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  StarIcon,
  UsersIcon
} from '@heroicons/react/24/outline'
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid'
import Link from 'next/link'

interface CourseLesson {
  id: string
  title: string
  description?: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  order: number
  duration?: number
  isPublished: boolean
  isFree: boolean
  video?: {
    id: string
    url: string
    duration?: number
  }
}

interface CourseChapter {
  id: string
  title: string
  description?: string
  order: number
  isPublished: boolean
  lessons: CourseLesson[]
}

interface CourseSection {
  id: string
  title: string
  description?: string
  order: number
  isPublished: boolean
  chapters: CourseChapter[]
}

interface Course {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  category?: string
  level?: string
  thumbnailImage?: string
  rating?: number
  studentsCount?: number
  instructor: string | {
    id: string
    name: string
    image?: string
  }
  sections: CourseSection[]
  totalLessons: number
  totalDuration: number
}

interface LessonProgress {
  id: string
  lessonId: string
  isCompleted: boolean
  watchTime: number
  lastPosition: number
  completedAt?: string
  lastAccessAt: string
}

interface CourseProgress {
  courseId: string
  progress: LessonProgress[]
  statistics: {
    totalLessons: number
    completedLessons: number
    totalWatchTime: number
    completionPercentage: number
    lastAccessedAt?: number
  }
}

export default function StudentCoursePage() {
  const params = useParams()
  const router = useRouter()
  const courseSlug = params?.slug as string

  const [course, setCourse] = useState<Course | null>(null)
  const [courseProgress, setCourseProgress] = useState<CourseProgress | null>(null)
  const [currentLesson, setCurrentLesson] = useState<CourseLesson | null>(null)
  const [loading, setLoading] = useState(true)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())
  const [videoPlaying, setVideoPlaying] = useState(false)

  useEffect(() => {
    if (courseSlug) {
      fetchCourse()
    }
  }, [courseSlug])

  const fetchCourse = async () => {
    try {
      console.log('Fetching course with slug:', courseSlug)
      const response = await fetch(`/api/student/courses/${courseSlug}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Course fetch failed:', response.status, response.statusText, errorData)
        throw new Error(errorData.message || `Failed to fetch course: ${response.status}`)
      }

      const data = await response.json()
      console.log('Course data received:', data)

      setCourse(data.course)

      // Set course progress if available
      if (data.courseProgress) {
        setCourseProgress(data.courseProgress)
      }

      // Set first lesson as current if no lesson is selected
      if (data.course.sections.length > 0) {
        const firstSection = data.course.sections[0]
        if (firstSection.chapters.length > 0) {
          const firstChapter = firstSection.chapters[0]
          if (firstChapter.lessons.length > 0) {
            setCurrentLesson(firstChapter.lessons[0])
          }
        }
      }

      // Expand all sections by default
      const sectionIds = new Set<string>(data.course.sections.map((s: CourseSection) => s.id))
      setExpandedSections(sectionIds)

      // Set loading to false since we got the data
      setLoading(false)
    } catch (error: any) {
      console.error('Error fetching course:', error)
      toast.error(error.message || 'Failed to load course')
      router.push('/student/courses')
    }
  }

  const fetchProgress = async () => {
    try {
      const response = await fetch(`/api/student/courses/progress?courseSlug=${courseSlug}`)
      if (!response.ok) throw new Error('Failed to fetch progress')

      const data = await response.json()
      setCourseProgress(data)
    } catch (error) {
      console.error('Error fetching progress:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateLessonProgress = async (lessonId: string, progressData: {
    watchTime?: number
    lastPosition?: number
    isCompleted?: boolean
  }) => {
    try {
      const response = await fetch('/api/student/courses/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lessonId,
          ...progressData
        })
      })

      if (!response.ok) throw new Error('Failed to update progress')

      // Refresh progress data
      fetchProgress()
    } catch (error) {
      console.error('Error updating progress:', error)
    }
  }

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const selectLesson = (lesson: CourseLesson) => {
    setCurrentLesson(lesson)
    // Mark lesson as accessed
    updateLessonProgress(lesson.id, {})
  }

  const markLessonComplete = (lessonId: string) => {
    updateLessonProgress(lessonId, { isCompleted: true })
  }

  const getLessonProgress = (lessonId: string): LessonProgress | undefined => {
    return courseProgress?.progress.find(p => p.lessonId === lessonId)
  }

  const isLessonCompleted = (lessonId: string): boolean => {
    const progress = getLessonProgress(lessonId)
    return progress?.isCompleted || false
  }

  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <PlayIcon className="w-4 h-4" />
      case 'TEXT':
        return <DocumentTextIcon className="w-4 h-4" />
      case 'QUIZ':
        return <AcademicCapIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0m'
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading course...</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl lg:text-2xl font-bold text-gray-800 dark:text-white mb-2">Course not found</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6 text-sm lg:text-base">The course you're looking for doesn't exist or you don't have access to it.</p>
          <Link href="/student/courses">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-4 lg:px-6 py-2 lg:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 text-sm lg:text-base"
            >
              Back to Courses
            </motion.button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border-b border-white/20 dark:border-slate-700/20 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 lg:py-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
            <div className="flex items-center space-x-4">
              <Link href="/student/courses">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 rounded-lg transition-colors duration-200"
                >
                  <ArrowLeftIcon className="w-5 h-5" />
                </motion.button>
              </Link>
              <div className="min-w-0 flex-1">
                <h1 className="text-lg lg:text-xl font-bold text-gray-800 dark:text-white truncate">{course.title}</h1>
                <div className="flex flex-wrap items-center gap-2 lg:gap-4 text-xs lg:text-sm text-gray-600 dark:text-gray-300 mt-1">
                  <span className="flex items-center">
                    <UsersIcon className="w-3 lg:w-4 h-3 lg:h-4 mr-1" />
                    <span className="hidden sm:inline">{course.studentsCount || 0} students</span>
                    <span className="sm:hidden">{course.studentsCount || 0}</span>
                  </span>
                  {course.rating && (
                    <span className="flex items-center">
                      <StarIcon className="w-3 lg:w-4 h-3 lg:h-4 mr-1 text-yellow-400 fill-current" />
                      {course.rating.toFixed(1)}
                    </span>
                  )}
                  <span className="hidden sm:inline">{course.level}</span>
                </div>
              </div>
            </div>

            {/* Progress Indicator */}
            {courseProgress && (
              <div className="flex flex-col lg:flex-row lg:items-center space-y-2 lg:space-y-0 lg:space-x-4">
                <div className="text-center lg:text-right">
                  <div className="text-sm lg:text-base font-medium text-gray-800 dark:text-white">
                    {courseProgress.statistics.completionPercentage}% Complete
                  </div>
                  <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-300">
                    {courseProgress.statistics.completedLessons} of {courseProgress.statistics.totalLessons} lessons
                  </div>
                </div>
                <div className="w-full lg:w-32 bg-gray-200 dark:bg-slate-700 rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${courseProgress.statistics.completionPercentage}%` }}
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 h-2 rounded-full transition-all duration-500"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-2">
            {currentLesson ? (
              <LessonPlayer
                lesson={currentLesson}
                progress={getLessonProgress(currentLesson.id)}
                onProgressUpdate={(progressData) => updateLessonProgress(currentLesson.id, progressData)}
                onComplete={() => markLessonComplete(currentLesson.id)}
              />
            ) : (
              <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8 text-center">
                <AcademicCapIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-800 mb-2">Welcome to the Course</h3>
                <p className="text-gray-600 mb-6">Select a lesson from the sidebar to start learning</p>
              </div>
            )}
          </div>

          {/* Course Sidebar */}
          <div className="lg:col-span-1">
            <CourseSidebar
              course={course}
              currentLesson={currentLesson}
              expandedSections={expandedSections}
              onToggleSection={toggleSection}
              onSelectLesson={selectLesson}
              getLessonProgress={getLessonProgress}
              isLessonCompleted={isLessonCompleted}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

interface LessonPlayerProps {
  lesson: CourseLesson
  progress?: LessonProgress
  onProgressUpdate: (progressData: { watchTime?: number; lastPosition?: number; isCompleted?: boolean }) => void
  onComplete: () => void
}

function LessonPlayer({ lesson, progress, onProgressUpdate, onComplete }: LessonPlayerProps) {
  const [videoCurrentTime, setVideoCurrentTime] = useState(0)
  const [videoDuration, setVideoDuration] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)

  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <PlayIcon className="w-4 h-4" />
      case 'TEXT':
        return <DocumentTextIcon className="w-4 h-4" />
      case 'QUIZ':
        return <AcademicCapIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  const handleVideoTimeUpdate = (currentTime: number) => {
    setVideoCurrentTime(currentTime)

    // Update progress every 10 seconds
    if (Math.floor(currentTime) % 10 === 0) {
      onProgressUpdate({
        watchTime: Math.floor(currentTime),
        lastPosition: Math.floor(currentTime)
      })
    }

    // Mark as complete when 90% watched
    if (videoDuration > 0 && currentTime / videoDuration >= 0.9 && !progress?.isCompleted) {
      onProgressUpdate({ isCompleted: true })
      onComplete()
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden"
    >
      {/* Lesson Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">{lesson.title}</h2>
            {lesson.description && (
              <p className="text-gray-600">{lesson.description}</p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center text-sm text-gray-600">
              {getLessonIcon(lesson.type)}
              <span className="ml-2">{lesson.type}</span>
            </div>
            {progress?.isCompleted && (
              <CheckCircleIconSolid className="w-6 h-6 text-green-500" />
            )}
          </div>
        </div>
      </div>

      {/* Lesson Content */}
      <div className="p-6">
        {lesson.type === 'VIDEO' && lesson.video ? (
          <div className="space-y-4">
            <div className="aspect-video bg-gray-900 rounded-xl overflow-hidden">
              <video
                src={lesson.video.url}
                controls
                className="w-full h-full"
                onTimeUpdate={(e) => handleVideoTimeUpdate(e.currentTarget.currentTime)}
                onLoadedMetadata={(e) => {
                  setVideoDuration(e.currentTarget.duration)
                  // Resume from last position
                  if (progress?.lastPosition) {
                    e.currentTarget.currentTime = progress.lastPosition
                  }
                }}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
              />
            </div>

            {/* Video Progress */}
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{formatTime(videoCurrentTime)} / {formatTime(videoDuration)}</span>
              <div className="flex items-center space-x-2">
                {progress?.watchTime && (
                  <span>Watch time: {formatTime(progress.watchTime)}</span>
                )}
                {progress?.isCompleted && (
                  <span className="flex items-center text-green-600">
                    <CheckCircleIconSolid className="w-4 h-4 mr-1" />
                    Completed
                  </span>
                )}
              </div>
            </div>
          </div>
        ) : lesson.type === 'TEXT' ? (
          <div className="prose max-w-none">
            <div className="bg-gray-50 rounded-xl p-6">
              <p className="text-gray-600 mb-4">Text lesson content would go here.</p>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  onProgressUpdate({ isCompleted: true })
                  onComplete()
                }}
                className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Mark as Complete
              </motion.button>
            </div>
          </div>
        ) : lesson.type === 'QUIZ' ? (
          <div className="bg-gray-50 rounded-xl p-6 text-center">
            <AcademicCapIcon className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Quiz Lesson</h3>
            <p className="text-gray-600 mb-4">Interactive quiz content would be loaded here.</p>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Start Quiz
            </motion.button>
          </div>
        ) : (
          <div className="bg-gray-50 rounded-xl p-6 text-center">
            <DocumentTextIcon className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Lesson Content</h3>
            <p className="text-gray-600">This lesson type is not yet supported.</p>
          </div>
        )}
      </div>
    </motion.div>
  )
}

interface CourseSidebarProps {
  course: Course
  currentLesson: CourseLesson | null
  expandedSections: Set<string>
  onToggleSection: (sectionId: string) => void
  onSelectLesson: (lesson: CourseLesson) => void
  getLessonProgress: (lessonId: string) => LessonProgress | undefined
  isLessonCompleted: (lessonId: string) => boolean
}

function CourseSidebar({
  course,
  currentLesson,
  expandedSections,
  onToggleSection,
  onSelectLesson,
  getLessonProgress,
  isLessonCompleted
}: CourseSidebarProps) {
  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <PlayIcon className="w-4 h-4" />
      case 'TEXT':
        return <DocumentTextIcon className="w-4 h-4" />
      case 'QUIZ':
        return <AcademicCapIcon className="w-4 h-4" />
      default:
        return <DocumentTextIcon className="w-4 h-4" />
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0m'
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m`
  }

  return (
    <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden">
      {/* Course Info */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3 mb-4">
          {course.thumbnailImage ? (
            <img
              src={course.thumbnailImage}
              alt={course.title}
              className="w-12 h-12 rounded-lg object-cover"
            />
          ) : (
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <AcademicCapIcon className="w-6 h-6 text-white" />
            </div>
          )}
          <div>
            <h3 className="font-semibold text-gray-800">
              {typeof course.instructor === 'string' ? course.instructor : course.instructor?.name || 'Instructor'}
            </h3>
            <p className="text-sm text-gray-600">Instructor</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="font-medium text-gray-800">{course.totalLessons}</div>
            <div className="text-gray-600">Lessons</div>
          </div>
          <div>
            <div className="font-medium text-gray-800">{formatDuration(course.totalDuration * 60)}</div>
            <div className="text-gray-600">Duration</div>
          </div>
        </div>
      </div>

      {/* Course Content */}
      <div className="max-h-96 overflow-y-auto">
        {course.sections.map((section) => (
          <div key={section.id} className="border-b border-gray-200 last:border-b-0">
            {/* Section Header */}
            <button
              onClick={() => onToggleSection(section.id)}
              className="w-full p-4 text-left hover:bg-gray-50/50 transition-colors duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {expandedSections.has(section.id) ? (
                    <ChevronDownIcon className="w-4 h-4 text-gray-500" />
                  ) : (
                    <ChevronRightIcon className="w-4 h-4 text-gray-500" />
                  )}
                  <span className="font-medium text-gray-800">{section.title}</span>
                </div>
                <span className="text-xs text-gray-500">
                  {section.chapters.reduce((acc, chapter) => acc + chapter.lessons.length, 0)} lessons
                </span>
              </div>
            </button>

            {/* Section Content */}
            <AnimatePresence>
              {expandedSections.has(section.id) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  {section.chapters.map((chapter) => (
                    <div key={chapter.id} className="pl-4">
                      {chapter.lessons.map((lesson) => {
                        const isCompleted = isLessonCompleted(lesson.id)
                        const isCurrent = currentLesson?.id === lesson.id

                        return (
                          <motion.button
                            key={lesson.id}
                            whileHover={{ scale: 1.01 }}
                            whileTap={{ scale: 0.99 }}
                            onClick={() => onSelectLesson(lesson)}
                            className={`w-full p-3 text-left border-l-2 transition-all duration-200 ${
                              isCurrent
                                ? 'border-blue-500 bg-blue-50/50'
                                : 'border-transparent hover:bg-gray-50/50'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="text-gray-600">
                                  {getLessonIcon(lesson.type)}
                                </div>
                                <div>
                                  <div className="text-sm font-medium text-gray-800">
                                    {lesson.title}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {lesson.type} • {formatDuration(lesson.duration)}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                {lesson.isFree && (
                                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                    Free
                                  </span>
                                )}
                                {isCompleted && (
                                  <CheckCircleIconSolid className="w-5 h-5 text-green-500" />
                                )}
                              </div>
                            </div>
                          </motion.button>
                        )
                      })}
                    </div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </div>
    </div>
  )
}
