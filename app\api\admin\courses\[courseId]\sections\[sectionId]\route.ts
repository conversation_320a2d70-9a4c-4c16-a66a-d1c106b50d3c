import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateSectionSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().optional(),
  isPublished: z.boolean().optional()
})

// GET /api/admin/courses/[courseId]/sections/[sectionId] - Get section details
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.courseId as string
      const sectionId = resolvedParams?.sectionId as string

      if (!courseId || !sectionId) {
        return APIResponse.error('Course ID and Section ID are required', 400)
      }

      const section = await prisma.courseSection.findFirst({
        where: { 
          id: sectionId,
          courseId 
        },
        include: {
          course: {
            select: {
              id: true,
              title: true
            }
          },
          chapters: {
            orderBy: { order: 'asc' },
            include: {
              lessons: {
                orderBy: { order: 'asc' },
                include: {
                  video: true
                }
              }
            }
          }
        }
      })

      if (!section) {
        return APIResponse.error('Section not found', 404)
      }

      // Calculate section statistics
      const totalLessons = section.chapters.reduce((acc, chapter) => 
        acc + chapter.lessons.length, 0)
      
      const totalDuration = section.chapters.reduce((acc, chapter) => 
        acc + chapter.lessons.reduce((lessonAcc, lesson) => 
          lessonAcc + (lesson.duration || 0), 0), 0)

      const sectionWithStats = {
        ...section,
        totalChapters: section.chapters.length,
        totalLessons,
        totalDuration: Math.round(totalDuration / 60) // Convert to minutes
      }

      return APIResponse.success({ section: sectionWithStats })
    } catch (error) {
      console.error('Error fetching section:', error)
      return APIResponse.error('Failed to fetch section', 500)
    }
  }
)

// PUT /api/admin/courses/[courseId]/sections/[sectionId] - Update section
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: updateSectionSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.courseId as string
      const sectionId = resolvedParams?.sectionId as string

      if (!courseId || !sectionId) {
        return APIResponse.error('Course ID and Section ID are required', 400)
      }

      // Check if section exists and belongs to the course
      const existingSection = await prisma.courseSection.findFirst({
        where: { 
          id: sectionId,
          courseId 
        }
      })

      if (!existingSection) {
        return APIResponse.error('Section not found', 404)
      }

      const updatedSection = await prisma.courseSection.update({
        where: { id: sectionId },
        data: validatedBody,
        include: {
          course: {
            select: {
              id: true,
              title: true
            }
          },
          chapters: {
            orderBy: { order: 'asc' },
            include: {
              lessons: {
                orderBy: { order: 'asc' },
                select: {
                  id: true,
                  title: true,
                  type: true,
                  duration: true,
                  isPublished: true
                }
              }
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Section updated successfully',
        section: updatedSection
      })
    } catch (error) {
      console.error('Error updating section:', error)
      return APIResponse.error('Failed to update section', 500)
    }
  }
)

// DELETE /api/admin/courses/[courseId]/sections/[sectionId] - Delete section
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.courseId as string
      const sectionId = resolvedParams?.sectionId as string

      if (!courseId || !sectionId) {
        return APIResponse.error('Course ID and Section ID are required', 400)
      }

      // Check if section exists and belongs to the course
      const section = await prisma.courseSection.findFirst({
        where: { 
          id: sectionId,
          courseId 
        },
        include: {
          chapters: {
            include: {
              lessons: true
            }
          }
        }
      })

      if (!section) {
        return APIResponse.error('Section not found', 404)
      }

      // Check if section has content
      const hasContent = section.chapters.some(chapter => chapter.lessons.length > 0)
      
      if (hasContent) {
        return APIResponse.error(
          'Cannot delete section with content. Please move or delete all lessons first.',
          400
        )
      }

      // Delete the section (this will cascade delete empty chapters)
      await prisma.courseSection.delete({
        where: { id: sectionId }
      })

      // Reorder remaining sections
      const remainingSections = await prisma.courseSection.findMany({
        where: { courseId },
        orderBy: { order: 'asc' }
      })

      // Update order to fill gaps
      await prisma.$transaction(
        remainingSections.map((section, index) =>
          prisma.courseSection.update({
            where: { id: section.id },
            data: { order: index + 1 }
          })
        )
      )

      return APIResponse.success({
        message: 'Section deleted successfully'
      })
    } catch (error) {
      console.error('Error deleting section:', error)
      return APIResponse.error('Failed to delete section', 500)
    }
  }
)
