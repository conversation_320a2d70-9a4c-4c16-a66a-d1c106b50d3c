import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createCategorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string().optional()
})

// GET /api/admin/courses/categories - Get all course categories
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      // Get unique categories from courses and count courses per category
      const categories = await prisma.course.groupBy({
        by: ['category'],
        where: {
          category: { not: null },
          isActive: true
        },
        _count: {
          category: true
        },
        orderBy: {
          _count: {
            category: 'desc'
          }
        }
      })

      // Transform to match expected format
      const formattedCategories = categories.map((cat, index) => ({
        id: `cat-${index}`, // Generate ID since we don't have a categories table
        name: cat.category || 'Uncategorized',
        description: `Category for ${cat.category} courses`,
        courseCount: cat._count.category,
        createdAt: new Date().toISOString()
      }))

      return APIResponse.success({ categories: formattedCategories })
    } catch (error) {
      console.error('Error fetching categories:', error)
      return APIResponse.error('Failed to fetch categories', 500)
    }
  }
)

// POST /api/admin/courses/categories - Create new category (placeholder)
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createCategorySchema
  },
  async (request: NextRequest, { validatedBody }) => {
    try {
      // Since we don't have a separate categories table,
      // this is a placeholder that would create a category
      // In a real implementation, you'd create a categories table
      
      const newCategory = {
        id: `cat-${Date.now()}`,
        name: validatedBody.name,
        description: validatedBody.description || '',
        courseCount: 0,
        createdAt: new Date().toISOString()
      }

      return APIResponse.success({
        message: 'Category created successfully',
        category: newCategory
      })
    } catch (error) {
      console.error('Error creating category:', error)
      return APIResponse.error('Failed to create category', 500)
    }
  }
)
